# Quick Admin Test
$baseUrl = "http://localhost/nexthomes"
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

Write-Host "Testing Admin Functionality..." -ForegroundColor Cyan

# Login as admin
$adminLoginData = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

try {
    $adminResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $adminLoginData -ContentType "application/json" -WebSession $session
    Write-Host "✓ Admin login successful" -ForegroundColor Green
    
    # Test admin stats
    $stats = Invoke-RestMethod -Uri "$baseUrl/api/admin/stats.php" -Method GET -WebSession $session
    Write-Host "✓ Admin stats: Products: $($stats.product_count), Orders: $($stats.order_count)" -ForegroundColor Green
    
    # Test admin products
    $products = Invoke-RestMethod -Uri "$baseUrl/api/admin/products/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ Admin products loaded: $($products.Count) products" -ForegroundColor Green
    
    # Test admin orders
    $orders = Invoke-RestMethod -Uri "$baseUrl/api/admin/orders/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ Admin orders loaded: $($orders.Count) orders" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Error: $($_.Exception.Message)" -ForegroundColor Red
}
