<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ?page=login');
    exit();
}

// Prevent admin users from accessing customer account page
if ($_SESSION['role'] === 'admin') {
    header('Location: ?page=admin_dashboard');
    exit();
}
?>

<div class="bg-white shadow rounded-lg p-6">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">My Account</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Account Information -->
        <div class="lg:col-span-1">
            <div class="bg-gray-50 p-4 rounded-lg">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Account Information</h2>
                <div class="space-y-2">
                    <p><strong>Name:</strong> <?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?></p>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['email']); ?></p>
                    <p><strong>Account Type:</strong> Customer</p>
                </div>
                <div class="mt-4">
                    <a href="/nexthomes/api/auth/logout.php" class="text-red-600 hover:text-red-800">Logout</a>
                </div>
            </div>
        </div>
        
        <!-- Order History -->
        <div class="lg:col-span-2">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Order History</h2>
            <div id="customer-orders-container">
                <p class="text-gray-500">Loading your orders...</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    fetchCustomerOrders();
});

async function fetchCustomerOrders() {
    const container = document.getElementById('customer-orders-container');
    try {
        const response = await fetch('/nexthomes/api/customer/orders.php');
        if (!response.ok) throw new Error('Failed to fetch orders.');
        const orders = await response.json();

        if (orders.length === 0) {
            container.innerHTML = '<p class="text-gray-500">You haven\'t placed any orders yet.</p>';
            return;
        }

        let ordersHTML = '<div class="space-y-4">';
        orders.forEach(order => {
            ordersHTML += `
                <div class="border rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold">Order #${order.id}</h3>
                            <p class="text-gray-600">Date: ${new Date(order.created_at).toLocaleDateString()}</p>
                            <p class="text-gray-600">Status: 
                                <span class="px-2 py-1 text-xs rounded-full ${order.order_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                    ${order.order_status}
                                </span>
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">GH₵ ${parseFloat(order.total_amount).toFixed(2)}</p>
                            <button class="text-blue-600 hover:text-blue-800 text-sm mt-1" onclick="toggleOrderDetails(${order.id})">
                                View Details
                            </button>
                        </div>
                    </div>
                    <div id="order-details-${order.id}" class="hidden mt-4 pt-4 border-t">
                        <p class="text-gray-500">Loading order details...</p>
                    </div>
                </div>
            `;
        });
        ordersHTML += '</div>';
        container.innerHTML = ordersHTML;

    } catch (error) {
        console.error("Failed to load orders:", error);
        container.innerHTML = '<p class="text-red-500">Failed to load your orders. Please try again later.</p>';
    }
}

async function toggleOrderDetails(orderId) {
    const detailsDiv = document.getElementById(`order-details-${orderId}`);
    
    if (detailsDiv.classList.contains('hidden')) {
        // Show details - fetch if not already loaded
        if (detailsDiv.innerHTML.includes('Loading')) {
            try {
                const response = await fetch(`/nexthomes/api/customer/order_detail.php?id=${orderId}`);
                if (!response.ok) throw new Error('Failed to fetch order details.');
                const order = await response.json();

                let itemsHTML = '<h4 class="font-semibold mb-2">Items:</h4><ul class="space-y-1">';
                order.items.forEach(item => {
                    itemsHTML += `
                        <li class="flex justify-between">
                            <span>${item.product_name} (x${item.quantity})</span>
                            <span>GH₵ ${(item.price_at_purchase * item.quantity).toFixed(2)}</span>
                        </li>
                    `;
                });
                itemsHTML += '</ul>';
                
                detailsDiv.innerHTML = itemsHTML;
            } catch (error) {
                detailsDiv.innerHTML = '<p class="text-red-500">Failed to load order details.</p>';
            }
        }
        detailsDiv.classList.remove('hidden');
    } else {
        detailsDiv.classList.add('hidden');
    }
}
</script>
