# Comprehensive NextHomes API Test Script
Write-Host "=== NextHomes Comprehensive API Test ===" -ForegroundColor Green

$baseUrl = "http://localhost/nexthomes"

# Test 1: Get All Products
Write-Host "`n1. Testing Get All Products..." -ForegroundColor Cyan
try {
    $products = Invoke-RestMethod -Uri "$baseUrl/api/products/get_all.php" -Method GET
    Write-Host "✓ SUCCESS: Found $($products.Count) products" -ForegroundColor Green
    $products | ForEach-Object { Write-Host "  - $($_.name): GH₵$($_.price) (Stock: $($_.stock_quantity))" }
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get Single Product
Write-Host "`n2. Testing Get Single Product (ID: 1)..." -ForegroundColor Cyan
try {
    $product = Invoke-RestMethod -Uri "$baseUrl/api/products/get_one.php?id=1" -Method GET
    Write-Host "✓ SUCCESS: Product '$($product.name)' - GH₵$($product.price)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test Registration
Write-Host "`n3. Testing User Registration..." -ForegroundColor Cyan
$randomEmail = "test$(Get-Random)@example.com"
$regData = @{
    first_name = "Test"
    last_name = "User"
    email = $randomEmail
    password = "testpass123"
} | ConvertTo-Json

try {
    $regResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/register.php" -Method POST -Body $regData -ContentType "application/json"
    Write-Host "✓ SUCCESS: User registered with email $randomEmail" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test Login with new user
Write-Host "`n4. Testing User Login..." -ForegroundColor Cyan
$loginData = @{
    email = $randomEmail
    password = "testpass123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "✓ SUCCESS: Login successful for $randomEmail" -ForegroundColor Green
    Write-Host "  Role: $($loginResponse.role)" -ForegroundColor Yellow
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test Admin Login
Write-Host "`n5. Testing Admin Login..." -ForegroundColor Cyan
$adminLoginData = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

try {
    $adminResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $adminLoginData -ContentType "application/json"
    Write-Host "✓ SUCCESS: Admin login successful" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test Cart Operations
Write-Host "`n6. Testing Cart Operations..." -ForegroundColor Cyan

# Add to cart
$cartData = @{
    productId = 1
    quantity = 2
} | ConvertTo-Json

try {
    $addResponse = Invoke-RestMethod -Uri "$baseUrl/api/cart/add.php" -Method POST -Body $cartData -ContentType "application/json"
    Write-Host "✓ SUCCESS: Added to cart. Total items: $($addResponse.cart_total_items)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Get cart contents
try {
    $cartContents = Invoke-RestMethod -Uri "$baseUrl/api/cart/get.php" -Method GET
    Write-Host "✓ SUCCESS: Cart has $($cartContents.items.Count) items, Subtotal: GH₵$($cartContents.subtotal)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Test Main Pages
Write-Host "`n7. Testing Main Pages..." -ForegroundColor Cyan

$pages = @("", "?page=home", "?page=cart", "?page=login", "?page=register")
foreach ($page in $pages) {
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/public/$page" -Method GET
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ SUCCESS: Page '$page' loaded (Status: $($response.StatusCode))" -ForegroundColor Green
        }
    } catch {
        Write-Host "✗ FAILED: Page '$page' - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "All basic functionality tests completed." -ForegroundColor Yellow
Write-Host "For admin functionality, please login through the web interface first." -ForegroundColor Yellow
Write-Host "Visit: $baseUrl/public/?page=login" -ForegroundColor Cyan
