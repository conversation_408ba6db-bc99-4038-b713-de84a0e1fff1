# Comprehensive NextHomes API Test Script with Session Management
Write-Host "=== NextHomes Comprehensive API Test ===" -ForegroundColor Green

$baseUrl = "http://localhost/nexthomes"

# Create a session container to maintain cookies across requests
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

# Test 1: Get All Products
Write-Host "`n1. Testing Get All Products..." -ForegroundColor Cyan
try {
    $products = Invoke-RestMethod -Uri "$baseUrl/api/products/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Found $($products.Count) products" -ForegroundColor Green
    $products | ForEach-Object { Write-Host "  - $($_.name): GH₵$($_.price) (Stock: $($_.stock_quantity))" }
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get Single Product
Write-Host "`n2. Testing Get Single Product (ID: 1)..." -ForegroundColor Cyan
try {
    $product = Invoke-RestMethod -Uri "$baseUrl/api/products/get_one.php?id=1" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Product '$($product.name)' - GH₵$($product.price)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test Registration
Write-Host "`n3. Testing User Registration..." -ForegroundColor Cyan
$randomEmail = "test$(Get-Random)@example.com"
$regData = @{
    first_name = "Test"
    last_name = "User"
    email = $randomEmail
    password = "testpass123"
} | ConvertTo-Json

try {
    $regResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/register.php" -Method POST -Body $regData -ContentType "application/json" -WebSession $session
    Write-Host "✓ SUCCESS: User registered with email $randomEmail" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test Login with new user
Write-Host "`n4. Testing Customer Login..." -ForegroundColor Cyan
$loginData = @{
    email = $randomEmail
    password = "testpass123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $loginData -ContentType "application/json" -WebSession $session
    Write-Host "✓ SUCCESS: Customer login successful for $randomEmail" -ForegroundColor Green
    Write-Host "  Role: $($loginResponse.role)" -ForegroundColor Yellow
} catch {
    Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test Customer Features (while logged in as customer)
Write-Host "`n5. Testing Customer Features..." -ForegroundColor Cyan

# Test customer orders
try {
    $customerOrders = Invoke-RestMethod -Uri "$baseUrl/api/customer/orders.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Customer orders retrieved ($($customerOrders.Count) orders)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Customer orders - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test Cart Operations (as customer)
Write-Host "`n6. Testing Cart Operations..." -ForegroundColor Cyan

# Add to cart
$cartData = @{
    productId = 1
    quantity = 2
} | ConvertTo-Json

try {
    $addResponse = Invoke-RestMethod -Uri "$baseUrl/api/cart/add.php" -Method POST -Body $cartData -ContentType "application/json" -WebSession $session
    Write-Host "✓ SUCCESS: Added to cart. Total items: $($addResponse.cart_total_items)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Add to cart - $($_.Exception.Message)" -ForegroundColor Red
}

# Get cart contents
try {
    $cartContents = Invoke-RestMethod -Uri "$baseUrl/api/cart/get.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Cart has $($cartContents.items.Count) items, Subtotal: GH₵$($cartContents.subtotal)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Get cart - $($_.Exception.Message)" -ForegroundColor Red
}

# Remove from cart
$removeData = @{
    productId = 1
} | ConvertTo-Json

try {
    $removeResponse = Invoke-RestMethod -Uri "$baseUrl/api/cart/remove.php" -Method POST -Body $removeData -ContentType "application/json" -WebSession $session
    Write-Host "✓ SUCCESS: Removed from cart. Total items: $($removeResponse.cart_total_items)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Remove from cart - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Logout and Test Admin Login
Write-Host "`n7. Testing Admin Login..." -ForegroundColor Cyan

# Logout first
try {
    Invoke-WebRequest -Uri "$baseUrl/api/auth/logout.php" -Method GET -WebSession $session | Out-Null
    Write-Host "✓ Customer logged out" -ForegroundColor Yellow
} catch {
    Write-Host "! Logout warning: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Admin login
$adminLoginData = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

try {
    $adminResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $adminLoginData -ContentType "application/json" -WebSession $session
    Write-Host "✓ SUCCESS: Admin login successful" -ForegroundColor Green
    Write-Host "  Role: $($adminResponse.role)" -ForegroundColor Yellow
} catch {
    Write-Host "✗ FAILED: Admin login - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Test Admin Features
Write-Host "`n8. Testing Admin Features..." -ForegroundColor Cyan

# Admin stats
try {
    $adminStats = Invoke-RestMethod -Uri "$baseUrl/api/admin/stats.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Admin stats - Products: $($adminStats.product_count), Orders: $($adminStats.order_count), Customers: $($adminStats.customer_count)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Admin stats - $($_.Exception.Message)" -ForegroundColor Red
}

# Admin get all products
try {
    $adminProducts = Invoke-RestMethod -Uri "$baseUrl/api/admin/products/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Admin products retrieved ($($adminProducts.Count) products)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Admin products - $($_.Exception.Message)" -ForegroundColor Red
}

# Admin get all orders
try {
    $adminOrders = Invoke-RestMethod -Uri "$baseUrl/api/admin/orders/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Admin orders retrieved ($($adminOrders.Count) orders)" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Admin orders - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Test Main Pages
Write-Host "`n9. Testing Main Pages..." -ForegroundColor Cyan

$pages = @("", "?page=home", "?page=cart", "?page=login", "?page=register")
foreach ($page in $pages) {
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/public/$page" -Method GET -WebSession $session
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ SUCCESS: Page '$page' loaded (Status: $($response.StatusCode))" -ForegroundColor Green
        }
    } catch {
        Write-Host "✗ FAILED: Page '$page' - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "✓ Product APIs tested" -ForegroundColor Green
Write-Host "✓ User registration and login tested" -ForegroundColor Green
Write-Host "✓ Cart operations tested" -ForegroundColor Green
Write-Host "✓ Customer features tested" -ForegroundColor Green
Write-Host "✓ Admin login and features tested" -ForegroundColor Green
Write-Host "✓ Main pages tested" -ForegroundColor Green
Write-Host "`nAll NextHomes features have been tested!" -ForegroundColor Yellow
Write-Host "Visit: $baseUrl/public/ to use the application" -ForegroundColor Cyan
