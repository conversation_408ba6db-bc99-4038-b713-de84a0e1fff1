<?php
// File: api/cart/add.php
session_start();

header('Content-Type: application/json');

// We expect a POST request with JSON data
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Only POST method is accepted.']);
    exit();
}

// Get the posted data
$data = json_decode(file_get_contents('php://input'), true);

// --- Input Validation ---
if (!isset($data['productId']) || !is_numeric($data['productId']) || !isset($data['quantity']) || !is_numeric($data['quantity'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Valid product ID and quantity are required.']);
    exit();
}

// Include necessary files
require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Cart.php';

$sessionId = session_id();
$productId = (int)$data['productId'];
$quantity = (int)$data['quantity'];

// --- Database and Model Instantiation ---
$db_instance = Database::getInstance();
$db_connection = $db_instance->getConnection();
$cartModel = new Cart($db_connection);

// --- Add item to cart ---
if ($cartModel->addOrUpdate($sessionId, $productId, $quantity)) {
    // On success, get the new total item count
    $totalItems = $cartModel->getTotalItemCount($sessionId);
    http_response_code(200); // OK
    echo json_encode(['success' => true, 'message' => 'Product added to cart.', 'cart_total_items' => $totalItems]);
} else {
    http_response_code(500); // Internal Server Error
    echo json_encode(['error' => 'Failed to add product to cart.']);
}