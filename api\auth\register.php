<?php
// File: api/auth/register.php
header('Content-Type: application/json');
require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/User.php';

$input = json_decode(file_get_contents('php://input'), true);

// Basic Validation
if (empty($input['first_name']) || empty($input['last_name']) || empty($input['email']) || empty($input['password'])) {
    http_response_code(400);
    echo json_encode(['error' => 'All fields are required.']);
    exit();
}

$db = Database::getInstance()->getConnection();
$userModel = new User($db);

if ($userModel->create($input)) {
    echo json_encode(['success' => true, 'message' => 'Account created successfully. You can now log in.']);
} else {
    http_response_code(409); // Conflict
    echo json_encode(['error' => 'An account with this email already exists.']);
}