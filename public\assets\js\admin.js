// Admin Panel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Load admin stats on dashboard
    if (window.location.search.includes('admin_dashboard')) {
        loadAdminStats();
    }
    
    // Load products on admin products page
    if (window.location.search.includes('admin_products')) {
        loadAdminProducts();
    }
    
    // Load orders on admin orders page
    if (window.location.search.includes('admin_orders')) {
        loadAdminOrders();
    }
});

// Load admin dashboard stats
async function loadAdminStats() {
    try {
        const response = await fetch('/nexthomes/api/admin/stats.php');
        const stats = await response.json();
        
        if (response.ok) {
            displayAdminStats(stats);
        } else {
            console.error('Failed to load admin stats:', stats);
            showStatsError();
        }
    } catch (error) {
        console.error('Error loading admin stats:', error);
        showStatsError();
    }
}

function displayAdminStats(stats) {
    const container = document.getElementById('admin-stats-container');
    if (!container) return;
    
    container.innerHTML = `
        <div class="dashboard-card">
            <h3>Total Products</h3>
            <div class="metric">${stats.product_count || 0}</div>
            <div class="description">Products in inventory</div>
        </div>
        <div class="dashboard-card">
            <h3>Total Orders</h3>
            <div class="metric">${stats.order_count || 0}</div>
            <div class="description">Orders received</div>
        </div>
        <div class="dashboard-card">
            <h3>Total Customers</h3>
            <div class="metric">${stats.customer_count || 0}</div>
            <div class="description">Registered customers</div>
        </div>
        <div class="dashboard-card">
            <h3>Total Revenue</h3>
            <div class="metric">GHC ${parseFloat(stats.total_revenue || 0).toFixed(2)}</div>
            <div class="description">Total sales revenue</div>
        </div>
    `;
}

function showStatsError() {
    const container = document.getElementById('admin-stats-container');
    if (!container) return;
    
    container.innerHTML = `
        <div class="dashboard-card" style="grid-column: 1 / -1; text-align: center; color: #dc2626;">
            <h3>Error Loading Stats</h3>
            <div class="description">Unable to load dashboard statistics. Please try refreshing the page.</div>
        </div>
    `;
}

// Load admin products
async function loadAdminProducts() {
    try {
        const response = await fetch('/nexthomes/api/admin/products/get_all.php');
        const products = await response.json();
        
        if (response.ok) {
            displayAdminProducts(products);
        } else {
            console.error('Failed to load admin products:', products);
            showProductsError();
        }
    } catch (error) {
        console.error('Error loading admin products:', error);
        showProductsError();
    }
}

function displayAdminProducts(products) {
    const container = document.getElementById('admin-products-container');
    if (!container) return;
    
    if (!products || products.length === 0) {
        container.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 2rem; color: #64748b;">
                    No products found. <a href="#" onclick="openAddProductModal()" style="color: #3b82f6;">Add your first product</a>
                </td>
            </tr>
        `;
        return;
    }
    
    container.innerHTML = products.map(product => `
        <tr>
            <td>
                <img src="${product.image_url || '/nexthomes/public/assets/images/placeholder.jpg'}" 
                     alt="${product.name}" 
                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
            </td>
            <td style="font-weight: 600; color: #1e293b;">${product.name}</td>
            <td style="color: #3b82f6; font-weight: 600;">GHC ${parseFloat(product.price).toFixed(2)}</td>
            <td>
                <span class="product-stock ${product.stock_quantity > 0 ? 'in-stock' : 'out-of-stock'}">
                    ${product.stock_quantity > 0 ? `In Stock (${product.stock_quantity})` : 'Out of Stock'}
                </span>
            </td>
            <td style="color: #64748b;">${new Date(product.created_at).toLocaleDateString()}</td>
            <td>
                <button onclick="editProduct(${product.id})" class="admin-btn admin-btn-primary" style="padding: 0.5rem 1rem; margin-right: 0.5rem;">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteProduct(${product.id})" class="admin-btn" style="background: #dc2626; color: white; padding: 0.5rem 1rem;">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function showProductsError() {
    const container = document.getElementById('admin-products-container');
    if (!container) return;
    
    container.innerHTML = `
        <tr>
            <td colspan="6" style="text-align: center; padding: 2rem; color: #dc2626;">
                Error loading products. Please try refreshing the page.
            </td>
        </tr>
    `;
}

// Load admin orders
async function loadAdminOrders() {
    try {
        const response = await fetch('/nexthomes/api/admin/orders/get_all.php');
        const orders = await response.json();
        
        if (response.ok) {
            displayAdminOrders(orders);
        } else {
            console.error('Failed to load admin orders:', orders);
            showOrdersError();
        }
    } catch (error) {
        console.error('Error loading admin orders:', error);
        showOrdersError();
    }
}

function displayAdminOrders(orders) {
    const container = document.getElementById('admin-orders-container');
    if (!container) return;
    
    if (!orders || orders.length === 0) {
        container.innerHTML = `
            <tr>
                <td colspan="5" style="text-align: center; padding: 2rem; color: #64748b;">
                    No orders found.
                </td>
            </tr>
        `;
        return;
    }
    
    container.innerHTML = orders.map(order => `
        <tr>
            <td style="font-weight: 600; color: #1e293b;">#${order.id}</td>
            <td>${order.customer_name || 'N/A'}</td>
            <td style="color: #3b82f6; font-weight: 600;">GHC ${parseFloat(order.total_amount).toFixed(2)}</td>
            <td>
                <span class="product-stock ${order.status === 'completed' ? 'in-stock' : 'out-of-stock'}">
                    ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </span>
            </td>
            <td style="color: #64748b;">${new Date(order.created_at).toLocaleDateString()}</td>
        </tr>
    `).join('');
}

function showOrdersError() {
    const container = document.getElementById('admin-orders-container');
    if (!container) return;
    
    container.innerHTML = `
        <tr>
            <td colspan="5" style="text-align: center; padding: 2rem; color: #dc2626;">
                Error loading orders. Please try refreshing the page.
            </td>
        </tr>
    `;
}

// Utility functions
function openAddProductModal() {
    alert('Add Product functionality will be implemented soon!');
}

function editProduct(id) {
    alert(`Edit Product ${id} functionality will be implemented soon!`);
}

function deleteProduct(id) {
    if (confirm('Are you sure you want to delete this product?')) {
        alert(`Delete Product ${id} functionality will be implemented soon!`);
    }
}

function exportData() {
    alert('Export Data functionality will be implemented soon!');
}

// Show loading state
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="admin-loading">
                <i class="fas fa-spinner"></i>
                Loading...
            </div>
        `;
    }
}
