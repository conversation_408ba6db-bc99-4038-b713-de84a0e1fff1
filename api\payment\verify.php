<?php
// File: api/payment/verify.php
session_start();

header('Content-Type: application/json');

require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Cart.php';
require_once '../../src/Models/Order.php';

// Get reference from the request
$input = json_decode(file_get_contents('php://input'), true);
$reference = $input['reference'] ?? null;

if (!$reference) {
    http_response_code(400);
    echo json_encode(['error' => 'No payment reference provided.']);
    exit();
}

// --- Verify Transaction with Paystack ---
$url = "https://api.paystack.co/transaction/verify/" . rawurlencode($reference);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . PAYSTACK_TEST_SECRET_KEY
]);

$response = curl_exec($ch);
$err = curl_error($ch);
curl_close($ch);

if ($err) {
    http_response_code(503); // Service Unavailable
    echo json_encode(['error' => 'Could not connect to payment provider.']);
    exit();
}

$result = json_decode($response);

if ($result->status == true && $result->data->status == 'success') {
    // --- Payment is successful, now create the order in our database ---
    $db_instance = Database::getInstance();
    $db_connection = $db_instance->getConnection();
    
    $orderModel = new Order($db_connection);
    $cartModel = new Cart($db_connection);

    $sessionId = session_id(); // You could also retrieve from Paystack metadata
    $cartItems = $cartModel->getContents($sessionId);
    
    // Check if order with this reference already exists to prevent duplicates
    $stmt = $db_connection->prepare("SELECT id FROM orders WHERE paystack_reference = ?");
    $stmt->execute([$reference]);
    if ($stmt->fetch()) {
        http_response_code(200);
        $orderId = $stmt->fetchColumn();
        echo json_encode(['success' => true, 'message' => 'Order already processed.', 'order_id' => $orderId]);
        exit();
    }

    // --- Prepare data for the Order model ---
    $paystack_data = $result->data;
    $orderData = [
        'customer_email' => $paystack_data->customer->email,
        'customer_name' => $paystack_data->metadata->first_name . ' ' . $paystack_data->metadata->last_name,
        'customer_address' => 'Address from metadata if available, otherwise default', // Placeholder
        'customer_phone' => 'Phone from metadata if available', // Placeholder
        'total_amount' => $paystack_data->amount / 100, // Convert back to GHS
        'paystack_reference' => $reference,
        'cart_items' => $cartItems,
        'session_id' => $sessionId
    ];

    $orderId = $orderModel->create($orderData);

    if ($orderId) {
        // Order created successfully
        http_response_code(200);
        echo json_encode(['success' => true, 'message' => 'Payment verified and order created.', 'order_id' => $orderId]);
    } else {
        // Database error
        http_response_code(500);
        echo json_encode(['error' => 'Payment was successful, but we could not save your order. Please contact support.']);
    }

} else {
    // Paystack said the payment was not successful
    http_response_code(400);
    echo json_encode(['error' => 'Payment verification failed.']);
}