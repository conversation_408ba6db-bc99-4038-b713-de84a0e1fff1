<?php
// File: src/Models/Order.php

class Order {
    private $db;

    public function countAll() { return $this->db->query("SELECT count(*) FROM orders")->fetchColumn(); }
    public function getRecent($limit = 5) {
    return $this->db->query("SELECT id, customer_name, total_amount, created_at FROM orders ORDER BY created_at DESC LIMIT " . (int)$limit)->fetchAll();
    }

     public function getAll() {
        $stmt = $this->db->prepare("SELECT id, customer_name, customer_email, total_amount, order_status, created_at FROM orders ORDER BY created_at DESC");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function findById($id) {
        $order = [];
        // Get main order details
        $stmt = $this->db->prepare("SELECT * FROM orders WHERE id = :id");
        $stmt->execute([':id' => $id]);
        $order['details'] = $stmt->fetch();

        if (!$order['details']) {
            return null; // Order not found
        }

        // Get associated order items
        $stmt = $this->db->prepare(
            "SELECT oi.*, p.name as product_name 
             FROM order_items oi
             JOIN products p ON oi.product_id = p.id
             WHERE oi.order_id = :id"
        );
        $stmt->execute([':id' => $id]);
        $order['items'] = $stmt->fetchAll();
        
        return $order;
    }

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    /**
     * Creates a new order, saves its items, and clears the cart.
     * This method uses a transaction to ensure data integrity.
     */
    public function create(array $data) {
        $this->db->beginTransaction();

        try {
            // 1. Insert into the main `orders` table
            $sql = "INSERT INTO orders (customer_email, customer_name, customer_address, customer_phone, total_amount, paystack_reference)
                    VALUES (:email, :name, :address, :phone, :amount, :reference)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':email' => $data['customer_email'],
                ':name' => $data['customer_name'],
                ':address' => $data['customer_address'],
                ':phone' => $data['customer_phone'],
                ':amount' => $data['total_amount'],
                ':reference' => $data['paystack_reference']
            ]);
            $orderId = $this->db->lastInsertId();

            // 2. Insert each item into the `order_items` table and update stock
            $sql = "INSERT INTO order_items (order_id, product_id, quantity, price_at_purchase)
                    VALUES (:order_id, :product_id, :quantity, :price)";
            $stmt = $this->db->prepare($sql);

            $updateStockSql = "UPDATE products SET stock_quantity = stock_quantity - :quantity WHERE id = :product_id";
            $updateStockStmt = $this->db->prepare($updateStockSql);

            foreach ($data['cart_items'] as $item) {
                // Insert order item
                $stmt->execute([
                    ':order_id' => $orderId,
                    ':product_id' => $item['product_id'],
                    ':quantity' => $item['quantity'],
                    ':price' => $item['price']
                ]);

                // Update product stock
                $updateStockStmt->execute([
                    ':quantity' => $item['quantity'],
                    ':product_id' => $item['product_id']
                ]);
            }

            // 3. Clear the user's cart
            $sql = "DELETE FROM cart_items WHERE session_id = :session_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':session_id', $data['session_id']);
            $stmt->execute();

            // If all went well, commit the transaction
            $this->db->commit();
            return $orderId;

        } catch (Exception $e) {
            // If anything went wrong, roll back the transaction
            $this->db->rollBack();
            error_log("Order creation failed: " . $e->getMessage());
            return false;
        }
    }
}