# Test Admin Login with different credentials
$baseUrl = "http://localhost/nexthomes"
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

Write-Host "Testing Admin Login with different credentials..." -ForegroundColor Cyan

# Try the admin user from the database
$adminEmails = @("<EMAIL>", "<EMAIL>")
$passwords = @("admin123", "password", "123456", "admin")

foreach ($email in $adminEmails) {
    foreach ($password in $passwords) {
        Write-Host "Trying: $email with password: $password" -ForegroundColor Yellow
        
        $loginData = @{
            email = $email
            password = $password
        } | ConvertTo-Json
        
        try {
            $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $loginData -ContentType "application/json" -WebSession $session
            Write-Host "✓ SUCCESS: Admin login successful with $email" -ForegroundColor Green
            Write-Host "  Role: $($response.role)" -ForegroundColor Yellow
            
            # Test admin stats if login successful
            try {
                $stats = Invoke-RestMethod -Uri "$baseUrl/api/admin/stats.php" -Method GET -WebSession $session
                Write-Host "✓ Admin stats work: Products: $($stats.product_count), Orders: $($stats.order_count)" -ForegroundColor Green
            } catch {
                Write-Host "✗ Admin stats failed: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            break
        } catch {
            Write-Host "✗ Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}
