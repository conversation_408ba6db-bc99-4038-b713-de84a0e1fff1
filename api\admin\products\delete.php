<?php // api/admin/products/delete.php
require_once '../../../src/Core/admin_auth.php';
header('Content-Type: application/json');
require_once '../../../config.php';
require_once '../../../src/Core/Database.php';
require_once '../../../src/Models/Product.php';

$input = json_decode(file_get_contents('php://input'), true);
$id = $input['id'] ?? null;

if (!$id) {
    http_response_code(400);
    echo json_encode(['error' => 'Product ID is required.']);
    exit();
}

$db = Database::getInstance()->getConnection();
$productModel = new Product($db);

if ($productModel->delete($id)) {
    echo json_encode(['success' => true, 'message' => 'Product deleted successfully.']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to delete product.']);
}