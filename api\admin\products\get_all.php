<?php // api/admin/products/get_all.php
require_once '../../../src/Core/admin_auth.php';
header('Content-Type: application/json');
require_once '../../../config.php';
require_once '../../../src/Core/Database.php';
require_once '../../../src/Models/Product.php';

$db = Database::getInstance()->getConnection();
$productModel = new Product($db);
$products = $productModel->getAllAdmin();
echo json_encode($products);