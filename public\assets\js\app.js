// File: public/assets/js/app.js (FINAL VERSION)

// File: public/assets/js/app.js (Complete up to Admin Product Management)

document.addEventListener('DOMContentLoaded', () => {
    // --- ELEMENT SELECTORS FOR ROUTING ---
    // Customer-facing
    const productListContainer = document.getElementById('product-list');
    const productDetailContainer = document.getElementById('product-detail-container');
    const cartContainer = document.getElementById('cart-container');
    const checkoutForm = document.getElementById('checkout-form');
    const verificationContainer = document.getElementById('verification-container');
    // Auth
    const registerForm = document.getElementById('register-form');
    const loginForm = document.getElementById('login-form');
    // Admin
    const adminDashboardContainer = document.getElementById('admin-stats-container');
    const productAdminContainer = document.getElementById('product-list-container');
    const orderListContainer = document.getElementById('order-list-container');
    const orderDetailContainer = document.getElementById('order-detail-container');


    // --- JAVASCRIPT ROUTER ---
    // Customer-facing
    if (productListContainer) { fetchProducts(); }
    if (productDetailContainer) {
        const productId = productDetailContainer.dataset.productId;
        if (productId) { fetchProductDetail(productId); }
    }
    if (cartContainer) { fetchCartContents(); }
    if (checkoutForm) {
        loadCheckoutSummary();
        checkoutForm.addEventListener('submit', handlePaymentInitialization);
    }
    if (verificationContainer) { handlePaymentVerification(); }
    
    // Auth
    if (registerForm) { registerForm.addEventListener('submit', handleRegistration); }
    if (loginForm) { loginForm.addEventListener('submit', handleLogin); }
    
    // Admin
    if (adminDashboardContainer) { fetchAdminDashboardData(); }
    // Note: productAdminContainer is also used for the public product list.
    // The check for `adminDashboardContainer` helps differentiate, but a more robust way
    // would be to use a unique ID for the admin product container if needed.
    // For now, this works because the admin-specific init function is called from the admin page.
    if (document.body.innerHTML.includes('Manage Products')) { // A simple way to check if we are on the admin products page
        initializeProductManagement();
    }

    if (orderListContainer) {
    fetchAndRenderOrders();
}
if (orderDetailContainer) {
    const orderId = orderDetailContainer.dataset.orderId;
    if (orderId) {
        fetchAndRenderOrderDetail(orderId);
    }
}



    // --- EVENT LISTENERS ---
    document.body.addEventListener('click', async (event) => {
        if (event.target.matches('.add-to-cart-btn')) {
            event.preventDefault();
            const button = event.target;
            const productId = button.dataset.productId;
            const quantityInput = document.getElementById('quantity');
            const quantity = quantityInput ? parseInt(quantityInput.value, 10) : 1;
            if (!productId || !quantity) return;
            await handleAddToCart(button, productId, quantity);
        }

        if (event.target.matches('.remove-from-cart-btn')) {
            event.preventDefault();
            const button = event.target;
            const productId = button.dataset.productId;
            if (!productId) return;
            await handleRemoveFromCart(button, productId);
        }
    });

    
    // ===================================================================
    // --- ALL FUNCTIONS DEFINED BELOW ---
    // ===================================================================

    // --- AUTHENTICATION FUNCTIONS ---
    async function handleRegistration(event) {
        event.preventDefault();
        const errorDiv = document.getElementById('register-error');
        errorDiv.textContent = '';
        const form = event.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        try {
            const response = await fetch('/nexthomes/api/auth/register.php', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(data) });
            const result = await response.json();
            if (!response.ok) throw new Error(result.error);
            window.location.href = '?page=login®istered=true';
        } catch (error) {
            errorDiv.textContent = error.message;
        }
    }

    async function handleLogin(event) {
        event.preventDefault();
        const errorDiv = document.getElementById('login-error');
        errorDiv.textContent = '';
        const form = event.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        try {
            const response = await fetch('/nexthomes/api/auth/login.php', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(data) });
            const result = await response.json();
            if (!response.ok) throw new Error(result.error);
            if (result.role === 'admin') {
                window.location.href = '?page=admin_dashboard';
            } else {
                window.location.href = '/nexthomes/public/';
            }
        } catch (error) {
            errorDiv.textContent = error.message;
        }
    }


    // --- NEW: Function to verify payment ---
    async function handlePaymentVerification() {
        const reference = verificationContainer.dataset.reference;
        const statusDiv = document.getElementById('verification-status');

        if (!reference || reference === 'none') {
            statusDiv.textContent = 'Error: No payment reference found. Please contact support.';
            return;
        }

        try {
            const response = await fetch('/nexthomes/api/payment/verify.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ reference: reference })
            });
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.error || 'Payment could not be verified.');
            }

            // Verification successful, redirect to the success page
            window.location.href = `?page=success&order_id=${result.order_id}`;

        } catch (error) {
            console.error("Verification failed:", error);
            statusDiv.textContent = `Verification Failed: ${error.message}. Please contact support with your payment reference: ${reference}`;
        }
    }

    // --- NEW: Function to load the order summary on the checkout page ---
    async function loadCheckoutSummary() {
        const summaryContainer = document.getElementById('checkout-summary');
        try {
            const response = await fetch('/nexthomes/api/cart/get.php');
            if (!response.ok) throw new Error('Failed to fetch cart data.');
            const cartData = await response.json();

            if (cartData.items.length === 0) {
                // If cart is empty, redirect back to cart page which will show "empty" message
                window.location.href = '?page=cart';
                return;
            }

            let itemsHTML = '<ul class="divide-y divide-gray-200">';
            cartData.items.forEach(item => {
                itemsHTML += `
                    <li class="py-2 flex justify-between">
                        <span class="text-gray-600">${escapeHTML(item.name)} (x${item.quantity})</span>
                        <span class="font-medium">GH₵ ${(item.price * item.quantity).toFixed(2)}</span>
                    </li>
                `;
            });
            itemsHTML += '</ul>';

            const summaryHTML = `
                ${itemsHTML}
                <div class="mt-4 pt-4 border-t flex justify-between items-center text-lg font-bold">
                    <span>Total</span>
                    <span>GH₵ ${cartData.subtotal.toFixed(2)}</span>
                </div>
            `;
            summaryContainer.innerHTML = summaryHTML;

        } catch (error) {
            console.error("Failed to load checkout summary:", error);
            summaryContainer.innerHTML = '<p class="text-red-500">Could not load summary.</p>';
        }
    }

    // --- NEW: Function to handle the "Pay Now" button click ---
    async function handlePaymentInitialization(event) {
        event.preventDefault(); // Stop the form from submitting normally

        const payButton = document.getElementById('pay-now-btn');
        const errorDiv = document.getElementById('payment-error');
        payButton.disabled = true;
        payButton.innerHTML = 'Processing...';
        errorDiv.textContent = '';

        // Collect form data
        const formData = new FormData(checkoutForm);
        const customerData = Object.fromEntries(formData.entries());

        try {
            const response = await fetch('/nexthomes/api/payment/initialize.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(customerData)
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.error || 'An unknown error occurred.');
            }

            // Redirect to Paystack's payment page
            window.location.href = result.authorization_url;

        } catch (error) {
            console.error("Payment initialization failed:", error);
            errorDiv.textContent = error.message;
            payButton.disabled = false;
            payButton.innerHTML = 'Pay Now';
        }
    }

    // --- FUNCTION DEFINITIONS ---

    async function handleAddToCart(button, productId, quantity) {
        // ... (this function remains the same)
        const originalButtonText = button.innerHTML;
        button.innerHTML = 'Adding...';
        button.disabled = true;
        try {
            const response = await fetch('/nexthomes/api/cart/add.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                body: JSON.stringify({ productId, quantity })
            });
            const result = await response.json();
            if (!response.ok) throw new Error(result.error || 'Failed to add to cart');
            const cartCountElement = document.getElementById('cart-item-count');
            if (cartCountElement) {
                cartCountElement.textContent = result.cart_total_items;
                cartCountElement.closest('a').classList.add('transform', 'scale-125', 'transition-transform', 'duration-300');
                setTimeout(() => cartCountElement.closest('a').classList.remove('transform', 'scale-125'), 300);
            }
            button.innerHTML = 'Added!';
            setTimeout(() => { button.innerHTML = originalButtonText; button.disabled = false; }, 2000);
        } catch (error) {
            console.error('Add to cart error:', error);
            button.innerHTML = 'Error!';
            setTimeout(() => { button.innerHTML = originalButtonText; button.disabled = false; }, 2000);
        }
    }

    async function handleRemoveFromCart(button, productId) {
        const originalButtonText = button.innerHTML;
        button.innerHTML = 'Removing...';
        button.disabled = true;
        try {
            const response = await fetch('/nexthomes/api/cart/remove.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                body: JSON.stringify({ productId })
            });
            const result = await response.json();
            if (!response.ok) throw new Error(result.error || 'Failed to remove from cart');

            // Update cart count in header
            const cartCountElement = document.getElementById('cart-item-count');
            if (cartCountElement) {
                cartCountElement.textContent = result.cart_total_items;
            }

            // Refresh cart contents if we're on the cart page
            if (cartContainer) {
                fetchCartContents();
            }

        } catch (error) {
            console.error('Remove from cart error:', error);
            button.innerHTML = 'Error!';
            setTimeout(() => { button.innerHTML = originalButtonText; button.disabled = false; }, 2000);
        }
    }

    // --- NEW: FUNCTION TO FETCH AND RENDER CART CONTENTS ---
    async function fetchCartContents() {
        const cartContainer = document.getElementById('cart-container');
        try {
            const response = await fetch('/nexthomes/api/cart/get.php');
            if (!response.ok) throw new Error('Failed to fetch cart data.');
            const cartData = await response.json();

            if (cartData.items.length === 0) {
                cartContainer.innerHTML = `
                    <p class="text-gray-600">Your cart is empty.</p>
                    <a href="/nexthomes/public/" class="mt-4 inline-block bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                        Continue Shopping
                    </a>
                `;
                return;
            }

            let itemsHTML = '';
            cartData.items.forEach(item => {
                const itemSubtotal = item.price * item.quantity;
                itemsHTML += `
                    <div class="flex items-center justify-between border-b py-4">
                        <div class="flex items-center gap-4">
                            <img src="${item.image_url}" alt="${escapeHTML(item.name)}" class="w-16 h-16 rounded object-cover">
                            <div>
                                <h3 class="font-semibold text-lg">${escapeHTML(item.name)}</h3>
                                <p class="text-gray-500">Price: GH₵ ${parseFloat(item.price).toFixed(2)}</p>
                                <button class="remove-from-cart-btn text-red-500 hover:text-red-700 text-sm mt-1" data-product-id="${item.product_id}">Remove</button>
                            </div>
                        </div>
                        <div class="text-right">
                            <p>Qty: ${item.quantity}</p>
                            <p class="font-semibold mt-1">GH₵ ${itemSubtotal.toFixed(2)}</p>
                        </div>
                    </div>
                `;
            });

            const summaryHTML = `
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <div class="flex justify-between items-center text-xl font-bold">
                        <span>Subtotal</span>
                        <span>GH₵ ${cartData.subtotal.toFixed(2)}</span>
                    </div>
                    <p class="text-gray-500 text-sm mt-2">Shipping and taxes calculated at checkout.</p>
                    <div class="mt-6">
        <a href="?page=checkout" id="checkout-btn" class="block text-center w-full bg-green-600 text-white py-3 px-6 rounded-md text-lg font-semibold hover:bg-green-700">
            Proceed to Checkout
        </a>
    </div>
                </div>
            `;

            cartContainer.innerHTML = itemsHTML + summaryHTML;

        } catch (error) {
            console.error("Failed to load cart:", error);
            cartContainer.innerHTML = '<p class="text-red-500">Could not load your cart. Please try again later.</p>';
        }
    }

    async function fetchProducts() {
        try {
            const response = await fetch('/nexthomes/api/products/get_all.php');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const products = await response.json();

            productListContainer.innerHTML = ''; 

            if (products.length === 0) {
                productListContainer.innerHTML = '<p class="text-gray-500 col-span-full">No products found.</p>';
                return;
            }

            products.forEach((product, index) => {
                const stockStatus = product.stock_quantity > 0 ? 'In Stock' : 'Out of Stock';
                const stockClass = product.stock_quantity > 0 ? 'text-green-600' : 'text-red-600';
                const isOutOfStock = product.stock_quantity === 0;

                const productCard = `
                    <div class="product-card animate-fade-in-up" style="animation-delay: ${index * 0.1}s">
                        <a href="?page=product&id=${product.id}" class="block">
                            <img src="${product.image_url}" alt="${escapeHTML(product.name)}" class="product-image">
                        </a>
                        <div class="p-6">
                            <a href="?page=product&id=${product.id}" class="block">
                                <h3 class="font-display text-xl font-semibold text-gray-800 hover:text-primary-600 transition-colors mb-2">${escapeHTML(product.name)}</h3>
                            </a>
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">${escapeHTML(product.description)}</p>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-2xl font-bold gradient-text">GH₵ ${parseFloat(product.price).toFixed(2)}</span>
                                <span class="text-xs ${stockClass} font-medium">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    ${stockStatus}
                                </span>
                            </div>
                            <button
                                class="add-to-cart-btn btn btn-primary w-full ${isOutOfStock ? 'opacity-50 cursor-not-allowed' : ''}"
                                data-product-id="${product.id}"
                                ${isOutOfStock ? 'disabled' : ''}
                            >
                                <i class="fas fa-shopping-cart"></i>
                                ${isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
                            </button>
                        </div>
                    </div>
                `;
                productListContainer.innerHTML += productCard;
            });
        } catch (error) {
            console.error("Failed to fetch products:", error);
            productListContainer.innerHTML = '<p class="text-red-500 col-span-full">Failed to load products. Please try again later.</p>';
        }
    }

    // --- THIS IS THE FULLY CORRECTED FUNCTION ---
    async function fetchProductDetail(id) {
        try {
            const response = await fetch(`/nexthomes/api/products/get_one.php?id=${id}`);
            if (!response.ok) {
                const errData = await response.json().catch(() => ({})); // try to parse json error, or default to empty obj
                throw new Error(errData.error || `HTTP error! status: ${response.status}`);
            }
            
            const product = await response.json();
            if (product.error) throw new Error(product.error);

            const stockStatus = product.stock_quantity > 0 
                ? `<span class="text-green-600 font-semibold">In Stock</span> (${product.stock_quantity} available)`
                : '<span class="text-red-600 font-semibold">Out of Stock</span>';
                
            const detailHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <img src="${product.image_url}" alt="${escapeHTML(product.name)}" class="w-full rounded-lg shadow-lg">
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-gray-900">${escapeHTML(product.name)}</h1>
                        <p class="text-3xl font-light text-gray-800 my-4">GH₵ ${parseFloat(product.price).toFixed(2)}</p>
                        <p class="text-gray-600 mt-4">${escapeHTML(product.description)}</p>
                        <div class="mt-6">
                            <p class="text-sm text-gray-500">${stockStatus}</p>
                        </div>
                        <div class="mt-6 flex items-center gap-4">
                            <label for="quantity" class="font-semibold">Quantity:</label>
                            <input type="number" id="quantity" name="quantity" value="1" min="1" max="${product.stock_quantity}" class="w-20 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div class="mt-8">
                            <button 
                                class="add-to-cart-btn w-full bg-blue-600 text-white py-3 px-6 rounded-md text-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:bg-gray-400"
                                data-product-id="${product.id}"
                                ${product.stock_quantity == 0 ? 'disabled' : ''}
                            >
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            `;
            productDetailContainer.innerHTML = detailHTML;

        } catch (error) {
            console.error("Failed to fetch product detail:", error);
            productDetailContainer.innerHTML = `<p class="text-red-500 text-center text-lg">${escapeHTML(error.message) || 'Failed to load product. Please check the URL or try again later.'}</p>`;
        }
    }

    // --- ADMIN FUNCTIONS ---
    async function fetchAdminDashboardData() {
        const statsContainer = document.getElementById('admin-stats-container');
        const ordersContainer = document.getElementById('recent-orders-container');
        try {
            const response = await fetch('/nexthomes/api/admin/stats.php');
            if (!response.ok) throw new Error('Failed to fetch dashboard data.');
            const data = await response.json();

            // Calculate revenue (assuming we have this data)
            const totalRevenue = data.recent_orders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

            statsContainer.innerHTML = `
                <div class="card card-body text-center group hover:scale-105 transition-transform duration-300">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Orders</h3>
                    <p class="text-3xl font-bold gradient-text mt-2">${data.order_count}</p>
                </div>
                <div class="card card-body text-center group hover:scale-105 transition-transform duration-300">
                    <div class="w-12 h-12 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                        <i class="fas fa-box text-white text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Products</h3>
                    <p class="text-3xl font-bold gradient-text mt-2">${data.product_count}</p>
                </div>
                <div class="card card-body text-center group hover:scale-105 transition-transform duration-300">
                    <div class="w-12 h-12 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Customers</h3>
                    <p class="text-3xl font-bold gradient-text mt-2">${data.customer_count}</p>
                </div>
                <div class="card card-body text-center group hover:scale-105 transition-transform duration-300">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Revenue</h3>
                    <p class="text-3xl font-bold gradient-text mt-2">GH₵ ${totalRevenue.toFixed(2)}</p>
                </div>
            `;

            if (data.recent_orders.length > 0) {
                let ordersHTML = '<div class="space-y-3">';
                data.recent_orders.slice(0, 5).forEach(order => {
                    ordersHTML += `
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-receipt text-white text-sm"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900">#${order.id}</p>
                                    <p class="text-sm text-gray-600">${escapeHTML(order.customer_name)}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900">GH₵ ${parseFloat(order.total_amount).toFixed(2)}</p>
                                <p class="text-sm text-gray-500">${new Date(order.created_at).toLocaleDateString()}</p>
                            </div>
                        </div>
                    `;
                });
                ordersHTML += '</div>';
                ordersHTML += '<div class="mt-4 text-center"><a href="?page=admin_orders" class="btn btn-outline btn-sm">View All Orders</a></div>';
                ordersContainer.innerHTML = ordersHTML;
            } else {
                ordersContainer.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-shopping-cart text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No recent orders found.</p>
                    </div>
                `;
            }

            // Load top products
            loadTopProducts(data.top_products);

        } catch (error) {
            statsContainer.innerHTML = `<div class="col-span-full card card-body text-center text-red-500"><i class="fas fa-exclamation-triangle text-2xl mb-2"></i><p>${error.message}</p></div>`;
            ordersContainer.innerHTML = '';
        }
    }

    function loadTopProducts(topProducts) {
        const container = document.getElementById('top-products');
        if (!container) return;

        if (topProducts && topProducts.length > 0) {
            let html = '<div class="space-y-3">';
            topProducts.forEach((product, index) => {
                const colors = ['primary', 'secondary', 'accent', 'green', 'purple'];
                const color = colors[index % colors.length];
                html += `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-${color}-500 to-${color}-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-bold">${index + 1}</span>
                            </div>
                            <span class="font-medium text-gray-900">${escapeHTML(product.name)}</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-600">${product.total_sold} sold</span>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;
        } else {
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-chart-pie text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">No sales data available.</p>
                </div>
            `;
        }
        } catch (error) {
            statsContainer.innerHTML = `<div class="col-span-full card card-body text-center text-red-500"><i class="fas fa-exclamation-triangle text-2xl mb-2"></i><p>${error.message}</p></div>`;
            ordersContainer.innerHTML = '';
        }
    }

    function initializeProductManagement() {
        const productListContainer = document.getElementById('product-list-container');
        const modal = document.getElementById('product-modal');
        const form = document.getElementById('product-form');
        const modalTitle = document.getElementById('modal-title');
        const productIdField = document.getElementById('product_id');

        const fetchAndRenderProducts = async () => {
            try {
                const response = await fetch('/nexthomes/api/admin/products/get_all.php');
                const products = await response.json();
                let tableHTML = '<table class="min-w-full bg-white"><thead><tr>' +
                    '<th class="py-2 px-4 border-b">ID</th><th class="py-2 px-4 border-b">Name</th>' +
                    '<th class="py-2 px-4 border-b">Price</th><th class="py-2 px-4 border-b">Stock</th>' +
                    '<th class="py-2 px-4 border-b">Actions</th></tr></thead><tbody>';
                products.forEach(p => {
                    tableHTML += `<tr>
                        <td class="py-2 px-4 border-b text-center">${p.id}</td>
                        <td class="py-2 px-4 border-b">${escapeHTML(p.name)}</td>
                        <td class="py-2 px-4 border-b text-right">GH₵ ${parseFloat(p.price).toFixed(2)}</td>
                        <td class="py-2 px-4 border-b text-center">${p.stock_quantity}</td>
                        <td class="py-2 px-4 border-b text-center">
                            <button class="edit-btn text-blue-500 hover:text-blue-700" data-product='${JSON.stringify(p)}'>Edit</button>
                            <button class="delete-btn text-red-500 hover:text-red-700 ml-2" data-id="${p.id}">Delete</button>
                        </td>
                    </tr>`;
                });
                tableHTML += '</tbody></table>';
                productListContainer.innerHTML = tableHTML;
            } catch (error) {
                productListContainer.innerHTML = '<p class="text-red-500">Failed to load products.</p>';
            }
        };

        const openModal = (product = null) => {
            form.reset();
            if (product) {
                modalTitle.textContent = 'Edit Product';
                productIdField.value = product.id;
                Object.keys(product).forEach(key => {
                    const field = form.elements[key];
                    if (field) field.value = product[key];
                });
            } else {
                modalTitle.textContent = 'Add Product';
                productIdField.value = '';
            }
            modal.classList.remove('hidden');
        };
        const closeModal = () => modal.classList.add('hidden');

        document.getElementById('add-product-btn').addEventListener('click', () => openModal());
        document.getElementById('cancel-btn').addEventListener('click', closeModal);
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            const isUpdating = data.id;
            const url = isUpdating ? '/nexthomes/api/admin/products/update.php' : '/nexthomes/api/admin/products/create.php';
            try {
                const response = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(data) });
                const result = await response.json();
                if (!response.ok) throw new Error(result.error);
                closeModal();
                fetchAndRenderProducts();
            } catch (error) {
                alert('Error saving product: ' + error.message);
            }
        });

        productListContainer.addEventListener('click', async (e) => {
            if (e.target.classList.contains('edit-btn')) {
                const productData = JSON.parse(e.target.dataset.product);
                openModal(productData);
            }
            if (e.target.classList.contains('delete-btn')) {
                const id = e.target.dataset.id;
                if (confirm('Are you sure you want to delete this product?')) {
                    try {
                        const response = await fetch('/nexthomes/api/admin/products/delete.php', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ id: id }) });
                        const result = await response.json();
                        if (!response.ok) throw new Error(result.error);
                        fetchAndRenderProducts();
                    } catch (error) {
                        alert('Error deleting product: ' + error.message);
                    }
                }
            }
        });

        fetchAndRenderProducts();
    }
    
    // --- ORDER MANAGEMENT FUNCTIONS ---
    async function fetchAndRenderOrders() {
        try {
            const response = await fetch('/nexthomes/api/admin/orders/get_all.php');
            if (!response.ok) throw new Error('Failed to fetch orders.');
            const orders = await response.json();

            let tableHTML = '<table class="min-w-full bg-white"><thead><tr>' +
                '<th class="py-2 px-4 border-b">Order ID</th>' +
                '<th class="py-2 px-4 border-b">Customer</th>' +
                '<th class="py-2 px-4 border-b">Email</th>' +
                '<th class="py-2 px-4 border-b">Amount</th>' +
                '<th class="py-2 px-4 border-b">Status</th>' +
                '<th class="py-2 px-4 border-b">Date</th>' +
                '<th class="py-2 px-4 border-b">Actions</th>' +
                '</tr></thead><tbody>';

            orders.forEach(order => {
                tableHTML += `<tr>
                    <td class="py-2 px-4 border-b text-center">#${order.id}</td>
                    <td class="py-2 px-4 border-b">${escapeHTML(order.customer_name)}</td>
                    <td class="py-2 px-4 border-b">${escapeHTML(order.customer_email)}</td>
                    <td class="py-2 px-4 border-b text-right">GH₵ ${parseFloat(order.total_amount).toFixed(2)}</td>
                    <td class="py-2 px-4 border-b text-center">
                        <span class="px-2 py-1 text-xs rounded-full ${order.order_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                            ${order.order_status}
                        </span>
                    </td>
                    <td class="py-2 px-4 border-b text-center">${new Date(order.created_at).toLocaleDateString()}</td>
                    <td class="py-2 px-4 border-b text-center">
                        <a href="?page=admin_order_detail&id=${order.id}" class="text-blue-500 hover:text-blue-700">View Details</a>
                    </td>
                </tr>`;
            });

            tableHTML += '</tbody></table>';
            orderListContainer.innerHTML = tableHTML;

        } catch (error) {
            console.error("Failed to load orders:", error);
            orderListContainer.innerHTML = '<p class="text-red-500">Failed to load orders. Please try again later.</p>';
        }
    }

    async function fetchAndRenderOrderDetail(orderId) {
        try {
            const response = await fetch(`/nexthomes/api/admin/orders/get_one.php?id=${orderId}`);
            if (!response.ok) throw new Error('Failed to fetch order details.');
            const order = await response.json();

            let itemsHTML = '<table class="min-w-full bg-white mt-4"><thead><tr>' +
                '<th class="py-2 px-4 border-b">Product</th>' +
                '<th class="py-2 px-4 border-b">Quantity</th>' +
                '<th class="py-2 px-4 border-b">Price</th>' +
                '<th class="py-2 px-4 border-b">Subtotal</th>' +
                '</tr></thead><tbody>';

            order.items.forEach(item => {
                const subtotal = item.quantity * item.price_at_purchase;
                itemsHTML += `<tr>
                    <td class="py-2 px-4 border-b">${escapeHTML(item.product_name)}</td>
                    <td class="py-2 px-4 border-b text-center">${item.quantity}</td>
                    <td class="py-2 px-4 border-b text-right">GH₵ ${parseFloat(item.price_at_purchase).toFixed(2)}</td>
                    <td class="py-2 px-4 border-b text-right">GH₵ ${subtotal.toFixed(2)}</td>
                </tr>`;
            });

            itemsHTML += '</tbody></table>';

            const orderHTML = `
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Customer Information</h3>
                            <p><strong>Name:</strong> ${escapeHTML(order.details.customer_name)}</p>
                            <p><strong>Email:</strong> ${escapeHTML(order.details.customer_email)}</p>
                            <p><strong>Phone:</strong> ${escapeHTML(order.details.customer_phone)}</p>
                            <p><strong>Address:</strong> ${escapeHTML(order.details.customer_address)}</p>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Order Information</h3>
                            <p><strong>Order ID:</strong> #${order.details.id}</p>
                            <p><strong>Status:</strong>
                                <span class="px-2 py-1 text-xs rounded-full ${order.details.order_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                    ${order.details.order_status}
                                </span>
                            </p>
                            <p><strong>Total Amount:</strong> GH₵ ${parseFloat(order.details.total_amount).toFixed(2)}</p>
                            <p><strong>Payment Reference:</strong> ${order.details.paystack_reference}</p>
                            <p><strong>Date:</strong> ${new Date(order.details.created_at).toLocaleString()}</p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold mb-4">Order Items</h3>
                        ${itemsHTML}
                    </div>
                </div>
            `;

            orderDetailContainer.innerHTML = orderHTML;

        } catch (error) {
            console.error("Failed to load order details:", error);
            orderDetailContainer.innerHTML = '<p class="text-red-500">Failed to load order details. Please try again later.</p>';
        }
    }

    // --- UTILITY FUNCTION ---
    function escapeHTML(str) {
        if (typeof str !== 'string') return '';
        const p = document.createElement('p');
        p.appendChild(document.createTextNode(str));
        return p.innerHTML;
    }

    // Note: The bodies of the unchanged customer-facing functions are omitted here for brevity.
    // You should ensure the full versions from previous steps are present in your file.
    // For example, fetchProducts(), fetchProductDetail(), etc.
});
