<?php
// File: api/products/get_all.php

// Set the header to output JSON
header('Content-Type: application/json');

// Include necessary files
require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Product.php';

// Instantiate the database and get the connection
$db_instance = Database::getInstance();
$db_connection = $db_instance->getConnection();

// Instantiate the Product model
$productModel = new Product($db_connection);

// Fetch all products
$products = $productModel->getAll();

// Output the products as a JSON string
echo json_encode($products);