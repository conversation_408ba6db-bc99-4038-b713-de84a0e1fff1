<?php
// File: api/payment/initialize.php
session_start();

header('Content-Type: application/json');

// Ensure this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit();
}

// Include necessary files
require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Cart.php';

// Get customer data from the form submission
$input = json_decode(file_get_contents('php://input'), true);

// Basic validation
if (empty($input['email']) || !filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['error' => 'A valid email is required.']);
    exit();
}

// --- IMPORTANT: Calculate amount on the server, not from the client ---
$db_instance = Database::getInstance();
$db_connection = $db_instance->getConnection();
$cartModel = new Cart($db_connection);
$sessionId = session_id();

$cartItems = $cartModel->getContents($sessionId);

if (empty($cartItems)) {
    http_response_code(400);
    echo json_encode(['error' => 'Your cart is empty.']);
    exit();
}

$totalAmount = 0;
foreach ($cartItems as $item) {
    $totalAmount += $item['price'] * $item['quantity'];
}

// --- Paystack API Call ---

// Paystack expects the amount in the lowest currency unit (pesewas for GHS)
// So, we multiply the amount in GHS by 100
$amountInPesewas = $totalAmount * 100; 

// The URL to initialize a transaction
$url = "https://api.paystack.co/transaction/initialize";

// The data to send to Paystack
$fields = [
    'email' => $input['email'],
    'amount' => $amountInPesewas,
    'callback_url' => 'http://localhost/nexthomes/public/?page=verify_payment', // Page to redirect to after payment
    'metadata' => [
        'first_name' => $input['first_name'],
        'last_name' => $input['last_name'],
        'session_id' => $sessionId // Store our session ID for later verification
    ]
];

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . PAYSTACK_TEST_SECRET_KEY,
    'Content-Type: application/json'
]);

// Execute the request
$response = curl_exec($ch);
$err = curl_error($ch);
curl_close($ch);

// --- Process the response from Paystack ---
if ($err) {
    // There was a cURL error
    http_response_code(500);
    echo json_encode(['error' => 'Payment processor error. Please try again later.']);
} else {
    $result = json_decode($response);
    if ($result->status == true) {
        // Transaction initialized successfully
        // Send the authorization URL back to the frontend
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'authorization_url' => $result->data->authorization_url
        ]);
    } else {
        // Paystack returned an error
        http_response_code(400);
        echo json_encode(['error' => $result->message]);
    }
}