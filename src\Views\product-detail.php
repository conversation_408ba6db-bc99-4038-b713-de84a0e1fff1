<?php
// We will get the product ID from the URL query string
$productId = $_GET['id'] ?? null;
?>

<div id="product-detail-container" class="bg-white shadow rounded-lg p-6" data-product-id="<?php echo htmlspecialchars($productId); ?>">
    <!-- Product details will be loaded here by JavaScript -->
    <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-800">Loading Product...</h1>
        <p class="text-gray-500 mt-4">Please wait while we fetch the details.</p>
    </div>
</div>