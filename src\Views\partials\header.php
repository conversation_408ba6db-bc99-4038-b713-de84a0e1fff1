<?php
// We can define a page title variable for each page
$pageTitle = $pageTitle ?? 'NextHomes - Your one-stop shop';

// Check if a user is logged in to dynamically change the navigation
$isUserLoggedIn = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 text-gray-800 font-sans">

    <!-- Navigation Bar -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="/nexthomes/public/" class="font-bold text-2xl text-blue-600">NextHomes</a>
                </div>

                <!-- Main Navigation Links (Center) -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="/nexthomes/public/" class="text-gray-600 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Home</a>
                        <a href="#" class="text-gray-600 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Products</a>
                        <a href="#" class="text-gray-600 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">About</a>
                    </div>
                </div>

                <!-- Right-side actions: Auth & Cart -->
                <div class="flex items-center space-x-4">
                    
                    <?php if ($isUserLoggedIn): ?>
                        <!-- If user is logged in -->
                        <div class="hidden md:block">
                            <span class="text-gray-700">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?>!</span>
                            <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
                                <a href="?page=admin_dashboard" class="text-gray-600 hover:text-blue-500 ml-4">Admin Panel</a>
                            <?php else: ?>
                                <a href="?page=my_account" class="text-gray-600 hover:text-blue-500 ml-4">My Account</a>
                            <?php endif; ?>
                            <a href="/nexthomes/api/auth/logout.php" class="text-gray-600 hover:text-blue-500 ml-4">Logout</a>
                        </div>
                    <?php else: ?>
                        <!-- If user is a guest -->
                        <div class="hidden md:block">
                            <a href="?page=login" class="text-gray-600 hover:text-blue-500">Login</a>
                            <span class="text-gray-400 mx-2">|</span>
                            <a href="?page=register" class="text-gray-600 hover:text-blue-500">Register</a>
                        </div>
                    <?php endif; ?>

                    <!-- Shopping Cart Icon -->
                    <a href="?page=cart" class="relative transition-transform duration-300 ease-in-out">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600 hover:text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span id="cart-item-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                    </a>
                    
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">