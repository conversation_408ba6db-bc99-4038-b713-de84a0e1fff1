<?php
// We can define a page title variable for each page
$pageTitle = $pageTitle ?? 'NextHomes - Your one-stop shop';

// Check if a user is logged in to dynamically change the navigation
$isUserLoggedIn = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom CSS for NextHomes -->
    <link rel="stylesheet" href="/nexthomes/public/assets/css/nexthomes.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'primary': ['Inter', 'sans-serif'],
                        'display': ['Playfair Display', 'serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-primary min-h-screen">

    <!-- Navigation Bar -->
    <nav class="navbar sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="/nexthomes/public/" class="navbar-brand flex items-center gap-2">
                        <i class="fas fa-home text-2xl"></i>
                        NextHomes
                    </a>
                </div>

                <!-- Main Navigation Links (Center) -->
                <div class="hidden md:block">
                    <div class="flex items-center space-x-8">
                        <a href="/nexthomes/public/" class="nav-link text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-primary-50">
                            <i class="fas fa-home mr-2"></i>Home
                        </a>
                        <a href="/nexthomes/public/?page=products" class="nav-link text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-primary-50">
                            <i class="fas fa-box mr-2"></i>Products
                        </a>
                        <a href="/nexthomes/public/?page=about" class="nav-link text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-primary-50">
                            <i class="fas fa-info-circle mr-2"></i>About
                        </a>
                    </div>
                </div>

                <!-- Right-side actions: Auth & Cart -->
                <div class="flex items-center space-x-6">

                    <?php if ($isUserLoggedIn): ?>
                        <!-- If user is logged in -->
                        <div class="hidden md:flex items-center space-x-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <span class="text-gray-700 font-medium">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?>!</span>
                            </div>
                            <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
                                <a href="?page=admin_dashboard" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-cog"></i>
                                    Admin Panel
                                </a>
                            <?php else: ?>
                                <a href="?page=my_account" class="btn btn-sm btn-outline">
                                    <i class="fas fa-user-circle"></i>
                                    My Account
                                </a>
                            <?php endif; ?>
                            <a href="/nexthomes/api/auth/logout.php" class="text-gray-500 hover:text-red-500 transition-colors">
                                <i class="fas fa-sign-out-alt"></i>
                            </a>
                        </div>
                    <?php else: ?>
                        <!-- If user is a guest -->
                        <div class="hidden md:flex items-center space-x-3">
                            <a href="?page=login" class="btn btn-sm btn-outline">
                                <i class="fas fa-sign-in-alt"></i>
                                Login
                            </a>
                            <a href="?page=register" class="btn btn-sm btn-primary">
                                <i class="fas fa-user-plus"></i>
                                Register
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- Shopping Cart Icon -->
                    <a href="?page=cart" class="relative group">
                        <div class="w-10 h-10 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-110">
                            <i class="fas fa-shopping-cart text-white"></i>
                        </div>
                        <span id="cart-item-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-md">0</span>
                    </a>

                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 animate-fade-in-up">