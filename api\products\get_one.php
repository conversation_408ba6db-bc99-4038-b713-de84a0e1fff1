<?php
// File: api/products/get_one.php

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *'); // In production, restrict this to your domain

// Include necessary files
require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Product.php';

// --- Input Validation ---
// Check if an ID is provided and is a number
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'A valid product ID is required.']);
    exit();
}

$id = intval($_GET['id']);

// --- Database and Model Instantiation ---
$db_instance = Database::getInstance();
$db_connection = $db_instance->getConnection();
$productModel = new Product($db_connection);

// --- Fetch Product ---
$product = $productModel->findById($id);

// --- Response ---
if ($product) {
    // Product found, return it
    http_response_code(200); // OK
    echo json_encode($product);
} else {
    // Product not found
    http_response_code(404); // Not Found
    echo json_encode(['error' => 'Product not found.']);
}