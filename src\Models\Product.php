<?php
// In src/Models/Product.php

class Product {
    private $db;

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    public function getAllAdmin() {
        $stmt = $this->db->prepare("SELECT * FROM products ORDER BY created_at DESC");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getAll() {
        $stmt = $this->db->prepare("SELECT id, name, description, price, image_url, stock_quantity FROM products WHERE stock_quantity > 0 ORDER BY created_at DESC");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM products WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function countAll() {
        return $this->db->query("SELECT count(*) FROM products")->fetchColumn();
    }

    // --- CORRECTED METHODS ---

    public function create(array $data) {
        $sql = "INSERT INTO products (name, description, price, stock_quantity, image_url) 
                VALUES (:name, :description, :price, :stock_quantity, :image_url)";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':name' => $data['name'],
            ':description' => $data['description'],
            ':price' => $data['price'],
            ':stock_quantity' => $data['stock_quantity'],
            ':image_url' => $data['image_url']
        ]);
    }

    public function update(array $data) {
        $sql = "UPDATE products SET 
                    name = :name, 
                    description = :description, 
                    price = :price, 
                    stock_quantity = :stock_quantity, 
                    image_url = :image_url 
                WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':name' => $data['name'],
            ':description' => $data['description'],
            ':price' => $data['price'],
            ':stock_quantity' => $data['stock_quantity'],
            ':image_url' => $data['image_url'],
            ':id' => $data['id']
        ]);
    }

    public function delete($id) {
        $sql = "DELETE FROM products WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([':id' => $id]);
    }
}