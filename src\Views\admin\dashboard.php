<?php require_once 'partials/header.php'; // Includes security check ?>

<!-- Admin Dashboard Header -->
<div class="mb-8">
    <div class="card card-body">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="font-display text-4xl font-bold gradient-text mb-2">Admin Dashboard</h1>
                <p class="text-gray-600">Welcome back! Here's what's happening with your store today.</p>
            </div>
            <div class="flex gap-3">
                <a href="?page=admin_products" class="btn btn-primary">
                    <i class="fas fa-box"></i>
                    Manage Products
                </a>
                <a href="?page=admin_orders" class="btn btn-secondary">
                    <i class="fas fa-shopping-cart"></i>
                    View Orders
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div id="admin-stats-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Stats will be loaded here by JavaScript -->
    <div class="card card-body text-center loading">
        <div class="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
        <div class="h-4 bg-gray-200 rounded mb-2"></div>
        <div class="h-6 bg-gray-200 rounded"></div>
    </div>
    <div class="card card-body text-center loading">
        <div class="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
        <div class="h-4 bg-gray-200 rounded mb-2"></div>
        <div class="h-6 bg-gray-200 rounded"></div>
    </div>
    <div class="card card-body text-center loading">
        <div class="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
        <div class="h-4 bg-gray-200 rounded mb-2"></div>
        <div class="h-6 bg-gray-200 rounded"></div>
    </div>
    <div class="card card-body text-center loading">
        <div class="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
        <div class="h-4 bg-gray-200 rounded mb-2"></div>
        <div class="h-6 bg-gray-200 rounded"></div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <div class="card card-body">
        <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-plus-circle text-primary-500 mr-2"></i>
            Quick Actions
        </h3>
        <div class="space-y-3">
            <button onclick="openAddProductModal()" class="btn btn-outline w-full justify-start">
                <i class="fas fa-plus"></i>
                Add New Product
            </button>
            <a href="?page=admin_orders" class="btn btn-outline w-full justify-start">
                <i class="fas fa-eye"></i>
                View All Orders
            </a>
            <button onclick="exportData()" class="btn btn-outline w-full justify-start">
                <i class="fas fa-download"></i>
                Export Data
            </button>
        </div>
    </div>

    <div class="lg:col-span-2">
        <div class="card card-body">
            <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-clock text-secondary-500 mr-2"></i>
                Recent Orders
            </h3>
            <div id="recent-orders-container">
                <!-- Recent orders will load here -->
                <div class="space-y-3">
                    <div class="loading h-16 rounded"></div>
                    <div class="loading h-16 rounded"></div>
                    <div class="loading h-16 rounded"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="card card-body">
        <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-chart-line text-accent-500 mr-2"></i>
            Sales Overview
        </h3>
        <div id="sales-chart" class="h-64 flex items-center justify-center text-gray-500">
            <div class="text-center">
                <i class="fas fa-chart-line text-4xl mb-2"></i>
                <p>Sales chart will be displayed here</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-chart-pie text-green-500 mr-2"></i>
            Top Products
        </h3>
        <div id="top-products" class="space-y-3">
            <!-- Top products will load here -->
            <div class="loading h-12 rounded"></div>
            <div class="loading h-12 rounded"></div>
            <div class="loading h-12 rounded"></div>
        </div>
    </div>
</div>

<?php require_once 'partials/footer.php'; ?>