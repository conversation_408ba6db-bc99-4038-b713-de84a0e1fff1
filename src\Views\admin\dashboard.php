<?php require_once 'partials/header.php'; // Includes security check ?>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Admin Dashboard Header -->
        <div class="admin-header">
            <div>
                <h1>Admin Dashboard</h1>
                <p style="color: #64748b; margin: 0.5rem 0 0;">Welcome back! Here's what's happening with your store today.</p>
            </div>
            <div class="admin-actions">
                <a href="?page=admin_products" class="admin-btn admin-btn-primary">
                    <i class="fas fa-box"></i>
                    Manage Products
                </a>
                <a href="?page=admin_orders" class="admin-btn admin-btn-secondary">
                    <i class="fas fa-shopping-cart"></i>
                    View Orders
                </a>
            </div>
        </div>

        <!-- Stats Cards -->
        <div id="admin-stats-container" class="dashboard-grid">
            <!-- Stats will be loaded here by JavaScript -->
            <div class="dashboard-card admin-loading">
                <i class="fas fa-spinner"></i>
                Loading stats...
            </div>
            <div class="dashboard-card admin-loading">
                <i class="fas fa-spinner"></i>
                Loading stats...
            </div>
            <div class="dashboard-card admin-loading">
                <i class="fas fa-spinner"></i>
                Loading stats...
            </div>
            <div class="dashboard-card admin-loading">
                <i class="fas fa-spinner"></i>
                Loading stats...
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="quick-actions-grid">
                <a href="#" onclick="openAddProductModal()" class="quick-action">
                    <i class="fas fa-plus"></i>
                    Add New Product
                </a>
                <a href="?page=admin_orders" class="quick-action">
                    <i class="fas fa-eye"></i>
                    View All Orders
                </a>
                <a href="#" onclick="exportData()" class="quick-action">
                    <i class="fas fa-download"></i>
                    Export Data
                </a>
            </div>
        </div>

    <div class="lg:col-span-2">
        <div class="card card-body">
            <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-clock text-secondary-500 mr-2"></i>
                Recent Orders
            </h3>
            <div id="recent-orders-container">
                <!-- Recent orders will load here -->
                <div class="space-y-3">
                    <div class="loading h-16 rounded"></div>
                    <div class="loading h-16 rounded"></div>
                    <div class="loading h-16 rounded"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="card card-body">
        <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-chart-line text-accent-500 mr-2"></i>
            Sales Overview
        </h3>
        <div id="sales-chart" class="h-64 flex items-center justify-center text-gray-500">
            <div class="text-center">
                <i class="fas fa-chart-line text-4xl mb-2"></i>
                <p>Sales chart will be displayed here</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <h3 class="font-display text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-chart-pie text-green-500 mr-2"></i>
            Top Products
        </h3>
        <div id="top-products" class="space-y-3">
            <!-- Top products will load here -->
            <div class="loading h-12 rounded"></div>
            <div class="loading h-12 rounded"></div>
            <div class="loading h-12 rounded"></div>
        </div>
    </div>
</div>

<?php require_once 'partials/footer.php'; ?>