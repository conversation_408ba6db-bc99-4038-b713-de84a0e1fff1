<?php
// File: api/admin/stats.php
require_once '../../src/Core/admin_auth.php'; // SECURE THE API
header('Content-Type: application/json');

require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Product.php';
require_once '../../src/Models/Order.php';
require_once '../../src/Models/User.php';

$db = Database::getInstance()->getConnection();
$productModel = new Product($db);
$orderModel = new Order($db);
$userModel = new User($db);

$stats = [
    'product_count' => $productModel->countAll(),
    'order_count' => $orderModel->countAll(),
    'customer_count' => $userModel->countAll(),
    'recent_orders' => $orderModel->getRecent(5)
];

echo json_encode($stats);