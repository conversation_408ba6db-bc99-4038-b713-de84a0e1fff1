<?php
// File: api/admin/stats.php
require_once '../../src/Core/admin_auth.php'; // SECURE THE API
header('Content-Type: application/json');

require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Product.php';
require_once '../../src/Models/Order.php';
require_once '../../src/Models/User.php';

$db = Database::getInstance()->getConnection();
$productModel = new Product($db);
$orderModel = new Order($db);
$userModel = new User($db);

// Get additional statistics
$db = Database::getInstance()->getConnection();

// Get total revenue
$revenueStmt = $db->query("SELECT SUM(total_amount) as total_revenue FROM orders WHERE order_status = 'paid'");
$totalRevenue = $revenueStmt->fetchColumn() ?: 0;

// Get low stock products
$lowStockStmt = $db->query("SELECT COUNT(*) FROM products WHERE stock_quantity < 5");
$lowStockCount = $lowStockStmt->fetchColumn();

// Get today's orders
$todayStmt = $db->query("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = CURDATE()");
$todayOrders = $todayStmt->fetchColumn();

// Get top selling products
$topProductsStmt = $db->query("
    SELECT p.name, SUM(oi.quantity) as total_sold
    FROM order_items oi
    JOIN products p ON oi.product_id = p.id
    GROUP BY p.id
    ORDER BY total_sold DESC
    LIMIT 5
");
$topProducts = $topProductsStmt->fetchAll();

$stats = [
    'product_count' => $productModel->countAll(),
    'order_count' => $orderModel->countAll(),
    'customer_count' => $userModel->countAll(),
    'total_revenue' => $totalRevenue,
    'low_stock_count' => $lowStockCount,
    'today_orders' => $todayOrders,
    'recent_orders' => $orderModel->getRecent(5),
    'top_products' => $topProducts
];

echo json_encode($stats);