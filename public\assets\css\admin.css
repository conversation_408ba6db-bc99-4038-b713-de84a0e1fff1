/* ===== ADMIN PANEL STYLES ===== */

/* Admin Layout */
.admin-container {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
}

.admin-sidebar {
  width: 250px;
  background: #1e293b;
  color: #ffffff;
  padding: 2rem 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.admin-brand {
  padding: 0 2rem 2rem;
  border-bottom: 1px solid #334155;
  margin-bottom: 2rem;
}

.admin-brand h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.admin-brand p {
  color: #94a3b8;
  font-size: 0.875rem;
  margin: 0.5rem 0 0;
}

.admin-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-nav li {
  margin: 0;
}

.admin-nav a {
  display: block;
  padding: 1rem 2rem;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.admin-nav a:hover,
.admin-nav a.active {
  background: #334155;
  color: #ffffff;
  border-left-color: #3b82f6;
}

.admin-nav a i {
  margin-right: 0.75rem;
  width: 1.25rem;
}

.admin-logout {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
}

.admin-logout a {
  display: block;
  padding: 0.75rem 1rem;
  background: #dc2626;
  color: #ffffff;
  text-decoration: none;
  border-radius: 8px;
  text-align: center;
  transition: background 0.3s ease;
}

.admin-logout a:hover {
  background: #b91c1c;
}

/* Main Content */
.admin-main {
  flex: 1;
  padding: 2rem;
  background: #ffffff;
  margin: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.admin-header h1 {
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.admin-actions {
  display: flex;
  gap: 1rem;
}

.admin-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.admin-btn-primary {
  background: #3b82f6;
  color: #ffffff;
}

.admin-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

.admin-btn-secondary {
  background: #6b7280;
  color: #ffffff;
}

.admin-btn-secondary:hover {
  background: #4b5563;
}

/* Dashboard Cards */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.dashboard-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
}

.dashboard-card .metric {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin: 0;
}

.dashboard-card .description {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0.5rem 0 0;
}

/* Quick Actions */
.quick-actions {
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 3rem;
}

.quick-actions h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.quick-action {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  text-decoration: none;
  color: #1e293b;
  transition: all 0.3s ease;
}

.quick-action:hover {
  background: #3b82f6;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.quick-action i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

/* Tables */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.admin-table th,
.admin-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.admin-table th {
  background: #f8fafc;
  color: #1e293b;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-table td {
  color: #64748b;
}

.admin-table tr:hover {
  background: #f8fafc;
}

/* Loading States */
.admin-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #64748b;
}

.admin-loading i {
  margin-right: 0.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .admin-container {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    position: relative;
  }
  
  .admin-main {
    margin: 0;
    border-radius: 0;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
