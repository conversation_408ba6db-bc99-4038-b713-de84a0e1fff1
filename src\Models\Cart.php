<?php
// File: src/Models/Cart.php

class Cart {
    private $db;

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    /**
     * Adds a product to the cart or updates its quantity if it already exists.
     * @param string $sessionId
     * @param int $productId
     * @param int $quantity
     * @return bool True on success, false on failure.
     */
    public function addOrUpdate($sessionId, $productId, $quantity) {
        try {
            // Using INSERT ... ON DUPLICATE KEY UPDATE is a very efficient MySQL feature.
            // It tries to INSERT a new row. If it fails because of the UNIQUE KEY we created
            // (on session_id and product_id), it will instead run the UPDATE part.
            $sql = "INSERT INTO cart_items (session_id, product_id, quantity) 
                    VALUES (:session_id, :product_id, :quantity)
                    ON DUPLICATE KEY UPDATE quantity = quantity + :quantity";
            
            $stmt = $this->db->prepare($sql);

            $stmt->bindParam(':session_id', $sessionId);
            $stmt->bindParam(':product_id', $productId, PDO::PARAM_INT);
            $stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);

            return $stmt->execute();

        } catch (PDOException $e) {
            // Log error in a real app
            error_log("Cart add/update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Gets the total number of items in a user's cart.
     * @param string $sessionId
     * @return int The total number of items.
     */
    public function getTotalItemCount($sessionId) {
        try {
            $stmt = $this->db->prepare("SELECT SUM(quantity) as total_items FROM cart_items WHERE session_id = :session_id");
            $stmt->bindParam(':session_id', $sessionId);
            $stmt->execute();
            $result = $stmt->fetch();
            // If the cart is empty, fetch() might return null or total_items as null.
            return $result && $result['total_items'] ? (int)$result['total_items'] : 0;
        } catch (PDOException $e) {
            error_log("Get total item count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Gets all items in the cart for a given session, with product details.
     * @param string $sessionId
     * @return array An array of cart items.
     */
    public function getContents($sessionId) {
        try {
            // This SQL query joins cart_items with products to get all necessary details in one go.
            $sql = "SELECT 
                        ci.product_id, 
                        ci.quantity,
                        p.name,
                        p.price,
                        p.image_url
                    FROM 
                        cart_items ci
                    JOIN 
                        products p ON ci.product_id = p.id
                    WHERE 
                        ci.session_id = :session_id
                    ORDER BY 
                        ci.added_at DESC"; // Order by most recently added
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':session_id', $sessionId);
            $stmt->execute();
            
            return $stmt->fetchAll();

        } catch (PDOException $e) {
            error_log("Get cart contents error: " . $e->getMessage());
            return []; // Return an empty array on error to prevent crashes
        }
    }
}