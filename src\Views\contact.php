<!-- Contact Hero Section -->
<section class="mb-16">
    <div class="card card-body text-center">
        <h1 class="font-display text-4xl md:text-5xl font-bold gradient-text mb-6">Get in Touch</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Have questions about our products or need help with your order? We're here to help! Reach out to us through any of the channels below.
        </p>
    </div>
</section>

<!-- Contact Information & Form -->
<section class="mb-16">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Contact Information -->
        <div class="animate-fade-in-up">
            <h2 class="font-display text-3xl font-bold text-gray-900 mb-8">Contact Information</h2>
            
            <div class="space-y-6">
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-map-marker-alt text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Address</h3>
                        <p class="text-gray-600">123 Furniture Street<br>Accra, Greater Accra Region<br>Ghana</p>
                    </div>
                </div>
                
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-phone text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Phone</h3>
                        <p class="text-gray-600">+233 24 123 4567<br>+233 20 987 6543</p>
                    </div>
                </div>
                
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Email</h3>
                        <p class="text-gray-600"><EMAIL><br><EMAIL></p>
                    </div>
                </div>
                
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Business Hours</h3>
                        <p class="text-gray-600">Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 4:00 PM<br>Sunday: Closed</p>
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="mt-8">
                <h3 class="font-semibold text-gray-900 mb-4">Follow Us</h3>
                <div class="flex gap-4">
                    <a href="#" class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-shadow">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-shadow">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-shadow">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gradient-to-r from-blue-700 to-blue-800 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-shadow">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Contact Form -->
        <div class="animate-slide-in-right">
            <div class="card card-body">
                <h2 class="font-display text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                
                <form id="contact-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="first_name" class="form-label">First Name *</label>
                            <input type="text" id="first_name" name="first_name" required class="form-input">
                        </div>
                        <div class="form-group">
                            <label for="last_name" class="form-label">Last Name *</label>
                            <input type="text" id="last_name" name="last_name" required class="form-input">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address *</label>
                        <input type="email" id="email" name="email" required class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" id="phone" name="phone" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="subject" class="form-label">Subject *</label>
                        <select id="subject" name="subject" required class="form-input">
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="product">Product Question</option>
                            <option value="order">Order Support</option>
                            <option value="complaint">Complaint</option>
                            <option value="suggestion">Suggestion</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message" class="form-label">Message *</label>
                        <textarea id="message" name="message" rows="5" required class="form-input resize-none"></textarea>
                    </div>
                    
                    <div id="form-message" class="hidden"></div>
                    
                    <button type="submit" class="btn btn-primary btn-lg w-full">
                        <i class="fas fa-paper-plane"></i>
                        Send Message
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="mb-16">
    <div class="text-center mb-12">
        <h2 class="font-display text-3xl font-bold gradient-text mb-4">Frequently Asked Questions</h2>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Quick answers to common questions
        </p>
    </div>
    
    <div class="max-w-4xl mx-auto space-y-4">
        <div class="card">
            <div class="card-body">
                <button class="faq-toggle w-full text-left flex items-center justify-between font-semibold text-gray-900 hover:text-primary-600 transition-colors">
                    <span>What is your return policy?</span>
                    <i class="fas fa-chevron-down transition-transform"></i>
                </button>
                <div class="faq-content hidden mt-4 text-gray-600">
                    <p>We offer a 30-day return policy for all items in their original condition. Items must be unused and in their original packaging. Return shipping costs are covered by the customer unless the item was damaged or defective.</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <button class="faq-toggle w-full text-left flex items-center justify-between font-semibold text-gray-900 hover:text-primary-600 transition-colors">
                    <span>How long does delivery take?</span>
                    <i class="fas fa-chevron-down transition-transform"></i>
                </button>
                <div class="faq-content hidden mt-4 text-gray-600">
                    <p>Standard delivery takes 3-7 business days within Accra and 5-10 business days for other regions in Ghana. Express delivery options are available for an additional fee.</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <button class="faq-toggle w-full text-left flex items-center justify-between font-semibold text-gray-900 hover:text-primary-600 transition-colors">
                    <span>Do you offer assembly services?</span>
                    <i class="fas fa-chevron-down transition-transform"></i>
                </button>
                <div class="faq-content hidden mt-4 text-gray-600">
                    <p>Yes, we offer professional assembly services for an additional fee. Our trained technicians can assemble your furniture at your location. Contact us for pricing and availability.</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <button class="faq-toggle w-full text-left flex items-center justify-between font-semibold text-gray-900 hover:text-primary-600 transition-colors">
                    <span>What payment methods do you accept?</span>
                    <i class="fas fa-chevron-down transition-transform"></i>
                </button>
                <div class="faq-content hidden mt-4 text-gray-600">
                    <p>We accept all major credit cards, mobile money (MTN, Vodafone, AirtelTigo), and bank transfers through our secure Paystack payment gateway.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // FAQ Toggle functionality
    document.querySelectorAll('.faq-toggle').forEach(button => {
        button.addEventListener('click', () => {
            const content = button.nextElementSibling;
            const icon = button.querySelector('i');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
    
    // Contact form submission
    document.getElementById('contact-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());
        const messageDiv = document.getElementById('form-message');
        const submitBtn = e.target.querySelector('button[type="submit"]');
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        
        // Simulate form submission (replace with actual API call)
        setTimeout(() => {
            messageDiv.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded';
            messageDiv.textContent = 'Thank you for your message! We\'ll get back to you within 24 hours.';
            messageDiv.classList.remove('hidden');
            
            // Reset form
            e.target.reset();
            
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Message';
            
            // Hide message after 5 seconds
            setTimeout(() => {
                messageDiv.classList.add('hidden');
            }, 5000);
        }, 2000);
    });
});
</script>
