<?php
// File: src/Core/admin_auth.php

// Ensure session is started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in AND if their role is 'admin'
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true || $_SESSION['user_role'] !== 'admin') {
    // If not an admin, redirect to the main login page
    header('Location: ?page=login&error=auth');
    exit();
}