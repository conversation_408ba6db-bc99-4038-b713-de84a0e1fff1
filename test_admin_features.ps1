# Test Admin Product and Order Management Features
Write-Host "=== Testing Admin Product and Order Management ===" -ForegroundColor Green

$baseUrl = "http://localhost/nexthomes"
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

# Login as admin
Write-Host "`nLogging in as admin..." -ForegroundColor Cyan
$adminLoginData = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

try {
    $adminResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login.php" -Method POST -Body $adminLoginData -ContentType "application/json" -WebSession $session
    Write-Host "✓ Admin login successful" -ForegroundColor Green
} catch {
    Write-Host "✗ Admin login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# Test Admin Product Management
Write-Host "`nTesting Admin Product Management..." -ForegroundColor Cyan

# Create a new product
$newProduct = @{
    name = "Test Product $(Get-Random)"
    description = "This is a test product created via API"
    price = 99.99
    image_url = "https://via.placeholder.com/400x300.png?text=Test+Product"
    stock_quantity = 10
} | ConvertTo-Json

try {
    $createResponse = Invoke-RestMethod -Uri "$baseUrl/api/admin/products/create.php" -Method POST -Body $newProduct -ContentType "application/json" -WebSession $session
    Write-Host "✓ SUCCESS: Product created" -ForegroundColor Green
    $productId = $createResponse.product_id
} catch {
    Write-Host "✗ FAILED: Product creation - $($_.Exception.Message)" -ForegroundColor Red
}

# Update the product
if ($productId) {
    $updateProduct = @{
        id = $productId
        name = "Updated Test Product"
        description = "This product has been updated"
        price = 149.99
        image_url = "https://via.placeholder.com/400x300.png?text=Updated+Product"
        stock_quantity = 15
    } | ConvertTo-Json

    try {
        $updateResponse = Invoke-RestMethod -Uri "$baseUrl/api/admin/products/update.php" -Method POST -Body $updateProduct -ContentType "application/json" -WebSession $session
        Write-Host "✓ SUCCESS: Product updated" -ForegroundColor Green
    } catch {
        Write-Host "✗ FAILED: Product update - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Get all products to verify
try {
    $allProducts = Invoke-RestMethod -Uri "$baseUrl/api/admin/products/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Retrieved $($allProducts.Count) products" -ForegroundColor Green
} catch {
    Write-Host "✗ FAILED: Get all products - $($_.Exception.Message)" -ForegroundColor Red
}

# Delete the test product
if ($productId) {
    $deleteProduct = @{
        id = $productId
    } | ConvertTo-Json

    try {
        $deleteResponse = Invoke-RestMethod -Uri "$baseUrl/api/admin/products/delete.php" -Method POST -Body $deleteProduct -ContentType "application/json" -WebSession $session
        Write-Host "✓ SUCCESS: Product deleted" -ForegroundColor Green
    } catch {
        Write-Host "✗ FAILED: Product deletion - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test Admin Order Management
Write-Host "`nTesting Admin Order Management..." -ForegroundColor Cyan

# Get all orders
try {
    $allOrders = Invoke-RestMethod -Uri "$baseUrl/api/admin/orders/get_all.php" -Method GET -WebSession $session
    Write-Host "✓ SUCCESS: Retrieved $($allOrders.Count) orders" -ForegroundColor Green
    
    if ($allOrders.Count -gt 0) {
        $firstOrderId = $allOrders[0].id
        Write-Host "  First order ID: $firstOrderId" -ForegroundColor Yellow
        
        # Get order details
        try {
            $orderDetail = Invoke-RestMethod -Uri "$baseUrl/api/admin/orders/get_one.php?id=$firstOrderId" -Method GET -WebSession $session
            Write-Host "✓ SUCCESS: Order details retrieved for order #$firstOrderId" -ForegroundColor Green
            Write-Host "  Customer: $($orderDetail.details.customer_name)" -ForegroundColor Yellow
            Write-Host "  Total: GH₵$($orderDetail.details.total_amount)" -ForegroundColor Yellow
            Write-Host "  Items: $($orderDetail.items.Count)" -ForegroundColor Yellow
        } catch {
            Write-Host "✗ FAILED: Get order details - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ FAILED: Get all orders - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Admin Features Test Complete ===" -ForegroundColor Green
Write-Host "✓ Product CRUD operations tested" -ForegroundColor Green
Write-Host "✓ Order management tested" -ForegroundColor Green
Write-Host "`nAll admin features are working correctly!" -ForegroundColor Yellow
