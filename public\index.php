<?php
session_start();

// File: public/index.php

// Load configuration and core classes
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../src/Core/Database.php';

// --- A Simple Router ---
// Get the page from the URL query string, default to 'home' if not set
$page = $_GET['page'] ?? 'home';

// --- Page-specific logic and view loading ---
// Use a switch statement to determine which view to load
switch ($page) {
    case 'product':
        $pageTitle = 'Product Details';
        $view = __DIR__ . '/../src/Views/product-detail.php';
        break;
    
    case 'cart':
        $pageTitle = 'Your Shopping Cart';
        $view = __DIR__ . '/../src/Views/cart.php';
        break;
    
    case 'home':
    default:
        $pageTitle = 'Home - NextHomes';
        $view = __DIR__ . '/../src/Views/home.php';
        break;
    // --- ADD THIS NEW CASE ---
    case 'checkout':
        $pageTitle = 'Checkout';
        $view = __DIR__ . '/../src/Views/checkout.php';
        break;
    case 'verify_payment':
        $pageTitle = 'Verifying Payment...';
        $view = __DIR__ . '/../src/Views/verify_payment.php';
    break;
    case 'success':
        $pageTitle = 'Order Successful!';
        $view = __DIR__ . '/../src/Views/success.php';
    break;
    case 'register':
    $pageTitle = 'Create an Account';
    $view = __DIR__ . '/../src/Views/register.php';
    break;
    case 'login':
    $pageTitle = 'Login';
    $view = __DIR__ . '/../src/Views/login.php';
    break;
    case 'admin_dashboard':
    $pageTitle = 'Admin Dashboard';
    // Note: We don't need a full view file for layout pages
    require_once __DIR__ . '/../src/Views/admin/dashboard.php';
    exit(); // Stop further processing since the view handles everything
    case 'admin_products':
    $pageTitle = 'Manage Products';
    require_once __DIR__ . '/../src/Views/admin/products.php';
    exit();
    case 'admin_orders':
    $pageTitle = 'Manage Orders';
    require_once DIR . '/../src/Views/admin/orders.php';
    exit();
    case 'admin_order_detail':
    $pageTitle = 'Order Details';
    require_once __DIR__ . '/../src/Views/admin/order_detail.php';
    exit();
}

// 1. Render the header. The $pageTitle variable is used inside header.php
require_once __DIR__ . '/../src/Views/partials/header.php';

// 2. Render the specific page view determined by the router
require_once $view;

// 3. Render the footer
require_once __DIR__ . '/../src/Views/partials/footer.php';

?>