<?php // api/admin/products/create.php
require_once '../../../src/Core/admin_auth.php';
header('Content-Type: application/json');
require_once '../../../config.php';
require_once '../../../src/Core/Database.php';
require_once '../../../src/Models/Product.php';

$input = json_decode(file_get_contents('php://input'), true);

$db = Database::getInstance()->getConnection();
$productModel = new Product($db);

if ($productModel->create($input)) {
    echo json_encode(['success' => true, 'message' => 'Product created successfully.']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create product.']);
}