<!-- Products Page Header -->
<section class="mb-12">
    <div class="card card-body text-center">
        <h1 class="font-display text-4xl md:text-5xl font-bold gradient-text mb-4">Our Products</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our complete collection of premium furniture and home decor items, carefully curated to bring elegance and comfort to your space.
        </p>
    </div>
</section>

<!-- Filters and Search -->
<section class="mb-8">
    <div class="card card-body">
        <div class="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input 
                        type="text" 
                        id="product-search" 
                        placeholder="Search products..." 
                        class="form-input pl-10 w-full"
                    >
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>
            
            <!-- Sort Options -->
            <div class="flex gap-4 items-center">
                <label class="text-sm font-medium text-gray-700">Sort by:</label>
                <select id="sort-products" class="form-input w-auto">
                    <option value="name-asc">Name (A-Z)</option>
                    <option value="name-desc">Name (Z-A)</option>
                    <option value="price-asc">Price (Low to High)</option>
                    <option value="price-desc">Price (High to Low)</option>
                    <option value="stock-desc">Stock (High to Low)</option>
                </select>
            </div>
            
            <!-- View Toggle -->
            <div class="flex gap-2">
                <button id="grid-view" class="btn btn-sm btn-outline active">
                    <i class="fas fa-th"></i>
                </button>
                <button id="list-view" class="btn btn-sm btn-outline">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Products Grid -->
<section>
    <div id="products-container">
        <div id="product-list" class="product-grid">
            <!-- Products will be loaded here -->
            <div class="card card-body text-center loading">
                <div class="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
            <div class="card card-body text-center loading">
                <div class="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
            <div class="card card-body text-center loading">
                <div class="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
        </div>
    </div>
    
    <!-- No Results Message -->
    <div id="no-results" class="hidden text-center py-12">
        <div class="card card-body">
            <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
            <h3 class="font-display text-2xl font-semibold text-gray-600 mb-2">No products found</h3>
            <p class="text-gray-500">Try adjusting your search or filter criteria</p>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', () => {
    let allProducts = [];
    let currentView = 'grid';
    
    // Load all products
    fetchAllProducts();
    
    // Search functionality
    document.getElementById('product-search').addEventListener('input', filterProducts);
    document.getElementById('sort-products').addEventListener('change', filterProducts);
    
    // View toggle
    document.getElementById('grid-view').addEventListener('click', () => setView('grid'));
    document.getElementById('list-view').addEventListener('click', () => setView('list'));
    
    async function fetchAllProducts() {
        try {
            const response = await fetch('/nexthomes/api/products/get_all.php');
            if (!response.ok) throw new Error('Failed to fetch products');
            allProducts = await response.json();
            displayProducts(allProducts);
        } catch (error) {
            console.error('Error fetching products:', error);
            document.getElementById('product-list').innerHTML = 
                '<div class="col-span-full text-center text-red-500">Failed to load products. Please try again later.</div>';
        }
    }
    
    function filterProducts() {
        const searchTerm = document.getElementById('product-search').value.toLowerCase();
        const sortBy = document.getElementById('sort-products').value;
        
        let filteredProducts = allProducts.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm)
        );
        
        // Sort products
        filteredProducts.sort((a, b) => {
            switch(sortBy) {
                case 'name-asc': return a.name.localeCompare(b.name);
                case 'name-desc': return b.name.localeCompare(a.name);
                case 'price-asc': return parseFloat(a.price) - parseFloat(b.price);
                case 'price-desc': return parseFloat(b.price) - parseFloat(a.price);
                case 'stock-desc': return b.stock_quantity - a.stock_quantity;
                default: return 0;
            }
        });
        
        displayProducts(filteredProducts);
    }
    
    function displayProducts(products) {
        const container = document.getElementById('product-list');
        const noResults = document.getElementById('no-results');
        
        if (products.length === 0) {
            container.classList.add('hidden');
            noResults.classList.remove('hidden');
            return;
        }
        
        container.classList.remove('hidden');
        noResults.classList.add('hidden');
        
        container.innerHTML = '';
        
        products.forEach((product, index) => {
            const stockStatus = product.stock_quantity > 0 ? 'In Stock' : 'Out of Stock';
            const stockClass = product.stock_quantity > 0 ? 'text-green-600' : 'text-red-600';
            const isOutOfStock = product.stock_quantity === 0;
            
            const productCard = `
                <div class="product-card animate-fade-in-up" style="animation-delay: ${index * 0.05}s">
                    <a href="?page=product&id=${product.id}" class="block">
                        <img src="${product.image_url}" alt="${escapeHTML(product.name)}" class="product-image">
                    </a>
                    <div class="p-6">
                        <a href="?page=product&id=${product.id}" class="block">
                            <h3 class="font-display text-xl font-semibold text-gray-800 hover:text-primary-600 transition-colors mb-2">${escapeHTML(product.name)}</h3>
                        </a>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">${escapeHTML(product.description)}</p>
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-2xl font-bold gradient-text">GH₵ ${parseFloat(product.price).toFixed(2)}</span>
                            <span class="text-xs ${stockClass} font-medium">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                ${stockStatus} (${product.stock_quantity})
                            </span>
                        </div>
                        <button 
                            class="add-to-cart-btn btn btn-primary w-full ${isOutOfStock ? 'opacity-50 cursor-not-allowed' : ''}"
                            data-product-id="${product.id}"
                            ${isOutOfStock ? 'disabled' : ''}
                        >
                            <i class="fas fa-shopping-cart"></i>
                            ${isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
                        </button>
                    </div>
                </div>
            `;
            container.innerHTML += productCard;
        });
    }
    
    function setView(view) {
        currentView = view;
        const gridBtn = document.getElementById('grid-view');
        const listBtn = document.getElementById('list-view');
        const container = document.getElementById('product-list');
        
        if (view === 'grid') {
            gridBtn.classList.add('btn-primary');
            gridBtn.classList.remove('btn-outline');
            listBtn.classList.add('btn-outline');
            listBtn.classList.remove('btn-primary');
            container.className = 'product-grid';
        } else {
            listBtn.classList.add('btn-primary');
            listBtn.classList.remove('btn-outline');
            gridBtn.classList.add('btn-outline');
            gridBtn.classList.remove('btn-primary');
            container.className = 'space-y-4';
        }
        
        displayProducts(allProducts);
    }
    
    function escapeHTML(str) {
        if (typeof str !== 'string') return '';
        const p = document.createElement('p');
        p.appendChild(document.createTextNode(str));
        return p.innerHTML;
    }
});
</script>
