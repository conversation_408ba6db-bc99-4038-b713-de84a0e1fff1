<?php
// File: api/cart/get.php
session_start();

header('Content-Type: application/json');

require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/Cart.php';

$sessionId = session_id();

$db_instance = Database::getInstance();
$db_connection = $db_instance->getConnection();
$cartModel = new Cart($db_connection);

// Fetch all items in the cart
$cartItems = $cartModel->getContents($sessionId);

// Calculate totals
$subtotal = 0;
foreach ($cartItems as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}

$response = [
    'items' => $cartItems,
    'subtotal' => $subtotal,
    'total_items' => $cartModel->getTotalItemCount($sessionId) // Reuse existing method
];

http_response_code(200);
echo json_encode($response);