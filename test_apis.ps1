# NextHomes API Testing Script
# This script tests all API endpoints to ensure they work correctly

$baseUrl = "http://localhost/nexthomes"
$apiUrl = "$baseUrl/api"

Write-Host "=== NextHomes API Testing Script ===" -ForegroundColor Green
Write-Host "Base URL: $baseUrl" -ForegroundColor Yellow
Write-Host ""

# Function to make HTTP requests and display results
function Test-API {
    param(
        [string]$Method = "GET",
        [string]$Endpoint,
        [hashtable]$Body = @{},
        [string]$Description,
        [hashtable]$Headers = @{}
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Cyan
    Write-Host "Method: $Method | Endpoint: $Endpoint" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = "$apiUrl$Endpoint"
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body.Count -gt 0) {
            $jsonBody = $Body | ConvertTo-Json
            $params.Body = $jsonBody
            $params.ContentType = "application/json"
            Write-Host "Request Body: $jsonBody" -ForegroundColor Gray
        }
        
        $response = Invoke-RestMethod @params
        Write-Host "✓ SUCCESS" -ForegroundColor Green
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor White
    }
    catch {
        Write-Host "✗ FAILED" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "Status Code: $statusCode" -ForegroundColor Red
        }
    }
    Write-Host "----------------------------------------" -ForegroundColor Gray
    Write-Host ""
}

# Test 1: Get All Products (Public API)
Test-API -Method "GET" -Endpoint "/products/get_all.php" -Description "Get All Products (Public)"

# Test 2: Get Single Product (Public API)
Test-API -Method "GET" -Endpoint "/products/get_one.php?id=1" -Description "Get Single Product (ID: 1)"

# Test 3: Get Single Product - Invalid ID
Test-API -Method "GET" -Endpoint "/products/get_one.php?id=999" -Description "Get Single Product (Invalid ID: 999)"

# Test 4: Add to Cart (Session-based)
Test-API -Method "POST" -Endpoint "/cart/add.php" -Body @{
    productId = 1
    quantity = 2
} -Description "Add Product to Cart"

# Test 5: Get Cart Contents
Test-API -Method "GET" -Endpoint "/cart/get.php" -Description "Get Cart Contents"

# Test 6: Remove from Cart
Test-API -Method "POST" -Endpoint "/cart/remove.php" -Body @{
    productId = 1
} -Description "Remove Product from Cart"

# Test 7: User Registration
$randomEmail = "test$(Get-Random)@example.com"
Test-API -Method "POST" -Endpoint "/auth/register.php" -Body @{
    first_name = "Test"
    last_name = "User"
    email = $randomEmail
    password = "testpassword123"
} -Description "User Registration"

# Test 8: User Login
Test-API -Method "POST" -Endpoint "/auth/login.php" -Body @{
    email = $randomEmail
    password = "testpassword123"
} -Description "User Login"

# Test 9: Admin Login (if admin exists)
Test-API -Method "POST" -Endpoint "/auth/login.php" -Body @{
    email = "<EMAIL>"
    password = "admin123"
} -Description "Admin Login"

Write-Host "=== Testing Admin APIs (Note: These require authentication) ===" -ForegroundColor Yellow

# Test 10: Admin Stats (requires admin session)
Test-API -Method "GET" -Endpoint "/admin/stats.php" -Description "Admin Dashboard Stats"

# Test 11: Admin Get All Products
Test-API -Method "GET" -Endpoint "/admin/products/get_all.php" -Description "Admin Get All Products"

# Test 12: Admin Get All Orders
Test-API -Method "GET" -Endpoint "/admin/orders/get_all.php" -Description "Admin Get All Orders"

# Test 13: Admin Get Single Order
Test-API -Method "GET" -Endpoint "/admin/orders/get_one.php?id=1" -Description "Admin Get Single Order"

Write-Host "=== Testing Customer APIs (Note: These require customer authentication) ===" -ForegroundColor Yellow

# Test 14: Customer Orders
Test-API -Method "GET" -Endpoint "/customer/orders.php" -Description "Customer Orders"

# Test 15: Customer Order Detail
Test-API -Method "GET" -Endpoint "/customer/order_detail.php?id=1" -Description "Customer Order Detail"

Write-Host "=== API Testing Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Note: Some tests may fail due to authentication requirements." -ForegroundColor Yellow
Write-Host "To fully test authenticated endpoints, you need to:" -ForegroundColor Yellow
Write-Host "1. Login through the web interface first" -ForegroundColor Yellow
Write-Host "2. Copy session cookies to the API requests" -ForegroundColor Yellow
Write-Host "3. Or test through the web interface directly" -ForegroundColor Yellow
