<?php
// File: api/customer/orders.php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required.']);
    exit();
}

// Prevent admin users from accessing customer API
if ($_SESSION['role'] === 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied.']);
    exit();
}

require_once '../../config.php';
require_once '../../src/Core/Database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    // Get orders for the logged-in customer
    $stmt = $db->prepare("
        SELECT id, customer_name, customer_email, total_amount, order_status, created_at 
        FROM orders 
        WHERE customer_email = :email 
        ORDER BY created_at DESC
    ");
    $stmt->execute([':email' => $_SESSION['email']]);
    $orders = $stmt->fetchAll();
    
    echo json_encode($orders);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch orders.']);
}
?>
