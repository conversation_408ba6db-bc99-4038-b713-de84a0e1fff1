<?php

// File: src/Core/Database.php

class Database {
    // We will store the single connection instance here
    private static $instance = null;
    private $conn;

    // Database connection details from our config file
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;

    // The constructor is private to prevent creating new instances from outside
    private function __construct() {
        // Data Source Name (DSN)
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname . ';charset=utf8mb4';

        try {
            // Create a PDO instance
            $this->conn = new PDO($dsn, $this->user, $this->pass);

            // Set PDO to throw exceptions on error for better error handling
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Set the default fetch mode to associative array for convenience
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            // If connection fails, stop the application and show the error
            die('Connection Error: ' . $e->getMessage());
        }
    }

    // The static method that controls the access to the single instance
    public static function getInstance() {
        if (self::$instance == null) {
            // This is the first time the method is called, create the instance
            self::$instance = new Database();
        }

        return self::$instance;
    }

    // A helper method to get the actual connection object
    public function getConnection() {
        return $this->conn;
    }
}