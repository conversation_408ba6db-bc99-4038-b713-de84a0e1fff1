<?php
// File: api/customer/order_detail.php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required.']);
    exit();
}

// Prevent admin users from accessing customer API
if ($_SESSION['role'] === 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied.']);
    exit();
}

$orderId = $_GET['id'] ?? null;
if (!$orderId) {
    http_response_code(400);
    echo json_encode(['error' => 'Order ID is required.']);
    exit();
}

require_once '../../config.php';
require_once '../../src/Core/Database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    // First, verify that this order belongs to the logged-in customer
    $stmt = $db->prepare("SELECT * FROM orders WHERE id = :id AND customer_email = :email");
    $stmt->execute([':id' => $orderId, ':email' => $_SESSION['email']]);
    $order = $stmt->fetch();
    
    if (!$order) {
        http_response_code(404);
        echo json_encode(['error' => 'Order not found or access denied.']);
        exit();
    }
    
    // Get order items
    $stmt = $db->prepare("
        SELECT oi.*, p.name as product_name 
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = :id
    ");
    $stmt->execute([':id' => $orderId]);
    $items = $stmt->fetchAll();
    
    echo json_encode([
        'details' => $order,
        'items' => $items
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch order details.']);
}
?>
