<?php
// File: api/auth/login.php
session_start();
header('Content-Type: application/json');

require_once '../../config.php';
require_once '../../src/Core/Database.php';
require_once '../../src/Models/User.php';

$input = json_decode(file_get_contents('php://input'), true);
$email = $input['email'] ?? '';
$password = $input['password'] ?? '';

if (empty($email) || empty($password)) {
    http_response_code(400);
    echo json_encode(['error' => 'Email and password are required.']);
    exit();
}

$db = Database::getInstance()->getConnection();
$userModel = new User($db);
$user = $userModel->findByEmail($email);

if ($user && password_verify($password, $user['password'])) {
    // Password is correct, set session variables
    $_SESSION['user_logged_in'] = true;
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = $user['first_name'];
    $_SESSION['user_role'] = $user['role']; // CRITICAL
    
    echo json_encode([
        'success' => true, 
        'message' => 'Login successful.',
        'role' => $user['role'] // Send role to frontend for redirection
    ]);
} else {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid email or password.']);
}