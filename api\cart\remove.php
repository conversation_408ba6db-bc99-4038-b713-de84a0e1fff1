<?php
// File: api/cart/remove.php
session_start();
header('Content-Type: application/json');

require_once '../../config.php';
require_once '../../src/Core/Database.php';

// Get the JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['productId'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Product ID is required.']);
    exit();
}

$productId = (int)$input['productId'];
$sessionId = session_id();

try {
    $db = Database::getInstance()->getConnection();
    
    // Remove the item from the cart
    $stmt = $db->prepare("DELETE FROM cart_items WHERE session_id = :session_id AND product_id = :product_id");
    $stmt->execute([
        ':session_id' => $sessionId,
        ':product_id' => $productId
    ]);
    
    // Get updated cart count
    $stmt = $db->prepare("SELECT COALESCE(SUM(quantity), 0) as total_items FROM cart_items WHERE session_id = :session_id");
    $stmt->execute([':session_id' => $sessionId]);
    $result = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'message' => 'Item removed from cart.',
        'cart_total_items' => (int)$result['total_items']
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to remove item from cart.']);
}
?>
