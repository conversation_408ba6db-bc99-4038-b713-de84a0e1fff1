<?php // api/admin/orders/get_one.php
require_once '../../../src/Core/admin_auth.php';
header('Content-Type: application/json');
require_once '../../../config.php';
require_once '../../../src/Core/Database.php';
require_once '../../../src/Models/Order.php';

$id = $_GET['id'] ?? null;
if (!$id) {
    http_response_code(400);
    echo json_encode(['error' => 'Order ID is required.']);
    exit();
}

$db = Database::getInstance()->getConnection();
$orderModel = new Order($db);
$order = $orderModel->findById($id);

if ($order) {
    echo json_encode($order);
} else {
    http_response_code(404);
    echo json_encode(['error' => 'Order not found.']);
}